<?php

/**
 * Migration Script for Online Database
 * 
 * This script helps migrate the local database structure to the online database
 * using the SSH tunnel connection.
 * 
 * Prerequisites:
 * 1. SSH tunnel must be established: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N
 * 2. Keep the SSH tunnel running in a separate terminal
 * 
 * Usage:
 * php migrate_to_online.php [action]
 * 
 * Actions:
 * - test: Test the database connection
 * - migrate: Run all migrations
 * - seed: Run the seeder to create initial admin user
 * - status: Check migration status
 * - all: Run test, migrate, and seed in sequence
 */

// Define paths
define('ROOTPATH', __DIR__ . DIRECTORY_SEPARATOR);
define('FCPATH', ROOTPATH . 'public' . DIRECTORY_SEPARATOR);
define('SYSTEMPATH', ROOTPATH . 'vendor/codeigniter4/framework/system/');
define('APPPATH', ROOTPATH . 'app' . DIRECTORY_SEPARATOR);
define('WRITEPATH', ROOTPATH . 'writable' . DIRECTORY_SEPARATOR);

// Load the paths config
$pathsConfig = APPPATH . 'Config/Paths.php';
$paths = require realpath($pathsConfig) ?: $pathsConfig;

// Load the framework bootstrap
require_once SYSTEMPATH . 'bootstrap.php';

// Load environment variables
$dotenv = \Dotenv\Dotenv::createImmutable(ROOTPATH);
if (file_exists(ROOTPATH . '.env')) {
    $dotenv->load();
}

// Bootstrap the application
$app = new \CodeIgniter\CodeIgniter($paths);
$app->initialize();

class OnlineMigrator
{
    private $db;
    private $forge;
    private $migration;

    public function __construct()
    {
        // Use the online database configuration
        $this->db = \Config\Database::connect('online');
        $this->forge = \Config\Database::forge('online');
        $this->migration = \Config\Services::migrations();
    }

    public function testConnection()
    {
        echo "Testing connection to online database...\n";
        
        try {
            $this->db->initialize();
            $query = $this->db->query("SELECT 1 as test");
            $result = $query->getRow();
            
            if ($result && $result->test == 1) {
                echo "✅ Connection successful!\n";
                echo "Database: " . $this->db->getDatabase() . "\n";
                echo "Host: " . $this->db->hostname . ":" . $this->db->port . "\n";
                return true;
            } else {
                echo "❌ Connection test failed\n";
                return false;
            }
        } catch (Exception $e) {
            echo "❌ Connection failed: " . $e->getMessage() . "\n";
            echo "\nTroubleshooting:\n";
            echo "1. Make sure SSH tunnel is running: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N\n";
            echo "2. Check if port 3307 is available\n";
            echo "3. Verify SSH credentials\n";
            return false;
        }
    }

    public function checkMigrationStatus()
    {
        echo "Checking migration status...\n";
        
        try {
            // Check if migrations table exists
            if (!$this->db->tableExists('migrations')) {
                echo "📋 Migrations table does not exist. Will be created during first migration.\n";
                return;
            }

            $query = $this->db->query("SELECT * FROM migrations ORDER BY version");
            $migrations = $query->getResult();
            
            if (empty($migrations)) {
                echo "📋 No migrations have been run yet.\n";
            } else {
                echo "📋 Applied migrations:\n";
                foreach ($migrations as $migration) {
                    echo "  - {$migration->version}: {$migration->class}\n";
                }
            }
        } catch (Exception $e) {
            echo "❌ Error checking migration status: " . $e->getMessage() . "\n";
        }
    }

    public function runMigrations()
    {
        echo "Running migrations on online database...\n";

        try {
            // Set the database group for migrations
            $config = new \Config\Migrations();
            $config->enabled = true;

            // Create migration runner with online database
            $migration = new \CodeIgniter\Database\MigrationRunner($config, $this->db);
            $migration->latest();

            echo "✅ Migrations completed successfully!\n";

            // Show created tables
            $tables = $this->db->listTables();
            echo "\n📋 Tables in database:\n";
            foreach ($tables as $table) {
                echo "  - {$table}\n";
            }

        } catch (Exception $e) {
            echo "❌ Migration failed: " . $e->getMessage() . "\n";
        }
    }

    public function runSeeder()
    {
        echo "Running seeder to create initial admin user...\n";

        try {
            // Create seeder instance with online database
            $seeder = new \CodeIgniter\Database\Seeder(new \Config\Database(), $this->db);
            $seeder->call('DakoiiUserSeeder');

            echo "✅ Seeder completed successfully!\n";
            echo "Initial admin user created with credentials:\n";
            echo "  Username: admin\n";
            echo "  Email: <EMAIL>\n";
            echo "  Password: admin123\n";
            echo "  Please change the password after first login.\n";

        } catch (Exception $e) {
            echo "❌ Seeder failed: " . $e->getMessage() . "\n";
        }
    }

    public function runAll()
    {
        echo "=== Starting complete migration process ===\n\n";
        
        if (!$this->testConnection()) {
            echo "\n❌ Cannot proceed without database connection.\n";
            return false;
        }
        
        echo "\n";
        $this->checkMigrationStatus();
        
        echo "\n";
        $this->runMigrations();
        
        echo "\n";
        $this->runSeeder();
        
        echo "\n=== Migration process completed ===\n";
        return true;
    }
}

// Main execution
$action = $argv[1] ?? 'help';
$migrator = new OnlineMigrator();

switch ($action) {
    case 'test':
        $migrator->testConnection();
        break;
        
    case 'status':
        $migrator->checkMigrationStatus();
        break;
        
    case 'migrate':
        $migrator->runMigrations();
        break;
        
    case 'seed':
        $migrator->runSeeder();
        break;
        
    case 'all':
        $migrator->runAll();
        break;
        
    default:
        echo "Online Database Migration Tool\n";
        echo "=============================\n\n";
        echo "Usage: php migrate_to_online.php [action]\n\n";
        echo "Actions:\n";
        echo "  test     - Test database connection\n";
        echo "  status   - Check migration status\n";
        echo "  migrate  - Run all migrations\n";
        echo "  seed     - Run seeder for initial admin user\n";
        echo "  all      - Run complete migration process\n\n";
        echo "Prerequisites:\n";
        echo "1. Start SSH tunnel: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N\n";
        echo "2. Keep tunnel running in separate terminal\n";
        echo "3. Run this script from the project root directory\n";
        break;
}
