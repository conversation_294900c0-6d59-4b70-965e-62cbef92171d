<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DakoiiUserSeeder extends Seeder
{
    public function run()
    {
        $data = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
            'first_name' => 'Super',
            'last_name' => 'Administrator',
            'phone_number' => '+************',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'two_factor_enabled' => 0,
        ];

        // Insert the initial super admin user
        $this->db->table('dakoii_users')->insert($data);
        
        echo "Initial Dakoii super admin user created:\n";
        echo "Username: admin\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
        echo "Please change the password after first login.\n";
    }
}
