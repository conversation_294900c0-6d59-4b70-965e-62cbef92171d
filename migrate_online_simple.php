<?php

/**
 * Simple Online Database Migration Script
 * 
 * This script temporarily updates the .env file to use the online database
 * and then runs CodeIgniter's built-in migration and seeder commands.
 */

class SimpleOnlineMigrator
{
    private $envFile = '.env';
    private $envBackup = '.env.backup';
    
    public function __construct()
    {
        if (!file_exists($this->envFile)) {
            die("❌ .env file not found. Please make sure you're in the project root directory.\n");
        }
    }
    
    public function testConnection()
    {
        echo "Testing connection to online database...\n";
        
        // Test with MySQL command
        $command = 'mysql -h 127.0.0.1 -P 3307 -u dakoiim1_chealthwokman_db_admin -pdakoiianzii -e "SELECT 1 as test;" dakoiim1_chealthwokman_db 2>&1';
        $output = shell_exec($command);
        
        if (strpos($output, 'test') !== false) {
            echo "✅ Connection successful!\n";
            return true;
        } else {
            echo "❌ Connection failed. Output:\n";
            echo $output . "\n";
            echo "\nTroubleshooting:\n";
            echo "1. Make sure SSH tunnel is running: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N\n";
            echo "2. Check if port 3307 is available\n";
            echo "3. Verify SSH credentials\n";
            return false;
        }
    }
    
    public function backupEnv()
    {
        echo "Backing up current .env file...\n";
        if (!copy($this->envFile, $this->envBackup)) {
            die("❌ Failed to backup .env file\n");
        }
        echo "✅ .env file backed up\n";
    }
    
    public function updateEnvForOnline()
    {
        echo "Updating .env for online database...\n";
        
        $envContent = file_get_contents($this->envFile);
        
        // Update database settings for online connection
        $envContent = preg_replace('/^database\.default\.hostname\s*=.*$/m', 'database.default.hostname = 127.0.0.1', $envContent);
        $envContent = preg_replace('/^database\.default\.database\s*=.*$/m', 'database.default.database = dakoiim1_chealthwokman_db', $envContent);
        $envContent = preg_replace('/^database\.default\.username\s*=.*$/m', 'database.default.username = dakoiim1_chealthwokman_db_admin', $envContent);
        $envContent = preg_replace('/^database\.default\.password\s*=.*$/m', 'database.default.password = dakoiianzii', $envContent);
        $envContent = preg_replace('/^database\.default\.port\s*=.*$/m', 'database.default.port = 3307', $envContent);
        
        if (file_put_contents($this->envFile, $envContent)) {
            echo "✅ .env file updated for online database\n";
            return true;
        } else {
            echo "❌ Failed to update .env file\n";
            return false;
        }
    }
    
    public function restoreEnv()
    {
        echo "Restoring original .env file...\n";
        if (file_exists($this->envBackup)) {
            if (copy($this->envBackup, $this->envFile)) {
                unlink($this->envBackup);
                echo "✅ Original .env file restored\n";
                return true;
            }
        }
        echo "❌ Failed to restore .env file\n";
        return false;
    }
    
    public function runMigrations()
    {
        echo "Running migrations...\n";
        
        $command = 'php spark migrate 2>&1';
        $output = shell_exec($command);
        
        echo $output . "\n";
        
        if (strpos($output, 'Done') !== false || strpos($output, 'Nothing to migrate') !== false) {
            echo "✅ Migrations completed\n";
            return true;
        } else {
            echo "❌ Migration may have failed. Check output above.\n";
            return false;
        }
    }
    
    public function runSeeder()
    {
        echo "Running seeder...\n";
        
        $command = 'php spark db:seed DakoiiUserSeeder 2>&1';
        $output = shell_exec($command);
        
        echo $output . "\n";
        
        if (strpos($output, 'Seeded') !== false || strpos($output, 'completed') !== false) {
            echo "✅ Seeder completed\n";
            echo "Initial admin user created:\n";
            echo "  Username: admin\n";
            echo "  Email: <EMAIL>\n";
            echo "  Password: admin123\n";
            return true;
        } else {
            echo "❌ Seeder may have failed. Check output above.\n";
            return false;
        }
    }
    
    public function showTables()
    {
        echo "Checking created tables...\n";
        
        $command = 'mysql -h 127.0.0.1 -P 3307 -u dakoiim1_chealthwokman_db_admin -pdakoiianzii -e "SHOW TABLES;" dakoiim1_chealthwokman_db 2>&1';
        $output = shell_exec($command);
        
        if ($output && !strpos($output, 'ERROR')) {
            echo "📋 Tables in database:\n";
            echo $output . "\n";
        } else {
            echo "❌ Could not list tables\n";
        }
    }
    
    public function runAll()
    {
        echo "=== Starting Online Database Migration ===\n\n";
        
        // Test connection first
        if (!$this->testConnection()) {
            echo "\n❌ Cannot proceed without database connection.\n";
            echo "Please ensure SSH tunnel is running:\n";
            echo "ssh -L 3307:127.0.0.1:3306 <EMAIL> -N\n";
            return false;
        }
        
        // Backup and update .env
        $this->backupEnv();
        if (!$this->updateEnvForOnline()) {
            $this->restoreEnv();
            return false;
        }
        
        try {
            // Run migrations
            echo "\n";
            $migrationSuccess = $this->runMigrations();
            
            // Run seeder
            echo "\n";
            $seederSuccess = $this->runSeeder();
            
            // Show tables
            echo "\n";
            $this->showTables();
            
            echo "\n=== Migration Process Completed ===\n";
            
            if ($migrationSuccess && $seederSuccess) {
                echo "✅ All operations completed successfully!\n";
            } else {
                echo "⚠️ Some operations may have failed. Please check the output above.\n";
            }
            
        } finally {
            // Always restore the original .env file
            echo "\n";
            $this->restoreEnv();
        }
        
        return true;
    }
}

// Main execution
$action = $argv[1] ?? 'help';
$migrator = new SimpleOnlineMigrator();

switch ($action) {
    case 'test':
        $migrator->testConnection();
        break;
        
    case 'migrate':
        $migrator->backupEnv();
        $migrator->updateEnvForOnline();
        $migrator->runMigrations();
        $migrator->restoreEnv();
        break;
        
    case 'seed':
        $migrator->backupEnv();
        $migrator->updateEnvForOnline();
        $migrator->runSeeder();
        $migrator->restoreEnv();
        break;
        
    case 'tables':
        $migrator->showTables();
        break;
        
    case 'all':
        $migrator->runAll();
        break;
        
    default:
        echo "Simple Online Database Migration Tool\n";
        echo "====================================\n\n";
        echo "Usage: php migrate_online_simple.php [action]\n\n";
        echo "Actions:\n";
        echo "  test     - Test database connection\n";
        echo "  migrate  - Run migrations only\n";
        echo "  seed     - Run seeder only\n";
        echo "  tables   - Show database tables\n";
        echo "  all      - Run complete migration process\n\n";
        echo "Prerequisites:\n";
        echo "1. Start SSH tunnel: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N\n";
        echo "2. Keep tunnel running in separate terminal\n";
        echo "3. Run this script from the project root directory\n";
        break;
}
