<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Dashboard</h1>
        <p class="text-muted mb-0">Welcome back, <?= esc($currentUser['first_name']) ?>! Here's what's happening with your system.</p>
    </div>
    <div>
        <span class="badge bg-success">
            <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
            System Online
        </span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Dakoii Users</div>
                        <div class="h2 mb-0 text-light"><?= number_format($stats['total_dakoii_users']) ?></div>
                        <div class="small text-success">
                            <i class="bi bi-arrow-up"></i>
                            <?= number_format($stats['active_dakoii_users']) ?> active
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people stats-icon text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Agencies</div>
                        <div class="h2 mb-0 text-light"><?= number_format($stats['total_agencies']) ?></div>
                        <div class="small text-success">
                            <i class="bi bi-arrow-up"></i>
                            <?= number_format($stats['active_agencies']) ?> active
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building stats-icon text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Admin Users</div>
                        <div class="h2 mb-0 text-light"><?= number_format($stats['total_admin_users']) ?></div>
                        <div class="small text-success">
                            <i class="bi bi-arrow-up"></i>
                            <?= number_format($stats['active_admin_users']) ?> active
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-badge stats-icon text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">System Health</div>
                        <div class="h2 mb-0 text-success">100%</div>
                        <div class="small text-success">
                            <i class="bi bi-check-circle"></i>
                            All systems operational
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-shield-check stats-icon text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Recent Activity -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity me-2"></i>
                    Recent Activity
                </h5>
                <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentActivity)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentActivity as $activity): ?>
                        <div class="list-group-item bg-transparent border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <?php
                                    $iconClass = 'bi-info-circle text-info';
                                    if ($activity['type'] === 'success') $iconClass = 'bi-check-circle text-success';
                                    if ($activity['type'] === 'warning') $iconClass = 'bi-exclamation-triangle text-warning';
                                    if ($activity['type'] === 'danger') $iconClass = 'bi-x-circle text-danger';
                                    ?>
                                    <i class="bi <?= $iconClass ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-light"><?= esc($activity['action']) ?></div>
                                    <div class="text-muted small"><?= esc($activity['description']) ?></div>
                                    <div class="text-muted small">
                                        <i class="bi bi-clock me-1"></i>
                                        <?= date('M j, Y g:i A', strtotime($activity['timestamp'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox display-4"></i>
                        <p class="mt-2">No recent activity</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- System Alerts -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bell me-2"></i>
                    System Alerts
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($systemAlerts)): ?>
                    <?php foreach ($systemAlerts as $alert): ?>
                    <div class="alert alert-<?= $alert['type'] ?> alert-dismissible fade show" role="alert">
                        <div class="fw-bold"><?= esc($alert['title']) ?></div>
                        <div class="small"><?= esc($alert['message']) ?></div>
                        <div class="small text-muted mt-1">
                            <i class="bi bi-clock me-1"></i>
                            <?= date('M j, g:i A', strtotime($alert['timestamp'])) ?>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-check-circle display-4 text-success"></i>
                        <p class="mt-2">No alerts</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/admin-users/create') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-person-plus display-6 mb-2"></i>
                            <span>Create Admin User</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/agencies/create') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-building-add display-6 mb-2"></i>
                            <span>Create Agency</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/system/audit-logs') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-journal-text display-6 mb-2"></i>
                            <span>View Audit Logs</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/reports') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-graph-up display-6 mb-2"></i>
                            <span>Generate Reports</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
