# Online Database Migration Guide

## Prerequisites

1. **Start SSH Tunnel** (keep this running in a separate terminal):
   ```bash
   ssh -L 3307:127.0.0.1:3306 <EMAIL> -N
   ```
   Password: `dakoiianzii`

2. **Verify XAMPP is running** with PHP available in PATH

## Migration Methods

### Method 1: Using Custom Migration Script (Recommended)

1. **Test Connection:**
   ```bash
   php migrate_to_online.php test
   ```

2. **Check Migration Status:**
   ```bash
   php migrate_to_online.php status
   ```

3. **Run Complete Migration:**
   ```bash
   php migrate_to_online.php all
   ```

4. **Individual Steps:**
   ```bash
   php migrate_to_online.php migrate  # Run migrations only
   php migrate_to_online.php seed     # Run seeder only
   ```

### Method 2: Using CodeIgniter Spark Commands

1. **Update .env file temporarily** to use online database:
   ```env
   database.default.hostname = 127.0.0.1
   database.default.database = dakoiim1_chealthwokman_db
   database.default.username = dakoiim1_chealthwokman_db_admin
   database.default.password = dakoiianzii
   database.default.port = 3307
   ```

2. **Run migrations:**
   ```bash
   php spark migrate
   ```

3. **Run seeder:**
   ```bash
   php spark db:seed DakoiiUserSeeder
   ```

4. **Restore .env file** to original local settings after migration

### Method 3: Using Windows Batch Script

1. **Run the setup script:**
   ```bash
   setup_online_migration.bat
   ```

2. **Follow the prompts** and use the opened terminals

## Database Tables to be Created

The migration will create these tables:

1. **dakoii_users** - Main user management table
2. **admin_users** - Admin portal users  
3. **agencies** - Agency management table
4. **migrations** - CodeIgniter migration tracking

## Initial Admin User

After running the seeder, you'll have:
- **Username:** admin
- **Email:** <EMAIL>  
- **Password:** admin123
- **Note:** Change password after first login

## Troubleshooting

### SSH Connection Issues
- Verify SSH tunnel is running: `netstat -an | findstr 3307`
- Check SSH password: `dakoiianzii`
- Ensure port 3307 is not in use by other applications

### Database Connection Issues
- Test with MySQL client: `mysql -h 127.0.0.1 -P 3307 -u dakoiim1_chealthwokman_db_admin -p`
- Verify database name: `dakoiim1_chealthwokman_db`
- Check if database exists on remote server

### Migration Issues
- Check migration files exist in `app/Database/Migrations/`
- Verify database user has CREATE/ALTER privileges
- Check CodeIgniter logs in `writable/logs/`

## Verification Steps

After migration, verify:

1. **Tables exist:**
   ```sql
   SHOW TABLES;
   ```

2. **Admin user created:**
   ```sql
   SELECT * FROM dakoii_users WHERE username = 'admin';
   ```

3. **Migration tracking:**
   ```sql
   SELECT * FROM migrations;
   ```

## Security Notes

- Change default admin password immediately after first login
- Consider creating additional admin users with proper roles
- Review and update agency data as needed
- Set up proper backup procedures for the online database
