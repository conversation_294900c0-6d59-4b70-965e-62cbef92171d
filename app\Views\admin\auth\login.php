<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin <PERSON>gin - CHealth Wokman | Christian Health Services PNG</title>
    <meta name="description" content="Admin Portal Login - CHealth Wokman Workforce Management System for Christian Health Services PNG">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Bootstrap -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #1A4E8C;
            --engine-red: #C8102E;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --gradient-primary: linear-gradient(135deg, #1A4E8C 0%, #2563EB 100%);
            --gradient-secondary: linear-gradient(135deg, #C8102E 0%, #EF4444 100%);
            --gradient-hero: linear-gradient(135deg, #1A4E8C 0%, #2563EB 50%, #C8102E 100%);
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: var(--gradient-hero);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
            padding: 2rem;
        }

        .login-card {
            background: var(--text-light);
            border-radius: 20px;
            padding: 3rem 2.5rem;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            text-align: center;
        }

        .login-logo {
            width: 100px;
            height: 100px;
            background: var(--text-light);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 15px;
        }

        .login-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .login-title {
            font-family: 'Roboto Condensed', sans-serif;
            font-weight: 700;
            font-size: 2rem;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #6c757d;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
        }

        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
        }

        .btn-login {
            background: var(--gradient-primary);
            border: none;
            padding: 1rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-radius: 12px;
            width: 100%;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 78, 140, 0.4);
        }

        .forgot-password {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--engine-red);
            text-decoration: underline;
        }

        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            z-index: 3;
        }

        .btn-back {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: var(--text-light);
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-back:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: var(--text-light);
            transform: translateY(-2px);
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background-color: rgba(200, 16, 46, 0.1);
            color: #721c24;
            border-left: 4px solid var(--engine-red);
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @media (max-width: 768px) {
            .login-container {
                padding: 1rem;
            }
            
            .login-card {
                padding: 2rem 1.5rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
            
            .back-home {
                top: 1rem;
                left: 1rem;
            }
            
            .btn-back {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements"></div>
    
    <!-- Back to Home -->
    <div class="back-home">
        <a href="<?= base_url() ?>" class="btn-back">
            <i class="material-icons me-2">arrow_back</i>
            Back to Home
        </a>
    </div>

    <div class="container">
        <div class="login-container">
            <div class="login-card">
                <!-- Logo -->
                <div class="login-logo">
                    <img src="<?= base_url('assets/images/chs-logo.png') ?>" alt="Christian Health Services PNG Logo">
                </div>
                
                <!-- Title -->
                <h1 class="login-title">Admin Portal</h1>
                <p class="login-subtitle">Christian Health Services PNG<br>Workforce Management System</p>
                
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="material-icons me-2">error</i>
                    <?= session()->getFlashdata('error') ?>
                </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success" role="alert">
                    <i class="material-icons me-2">check_circle</i>
                    <?= session()->getFlashdata('success') ?>
                </div>
                <?php endif; ?>

                <!-- Login Form -->
                <?= form_open('/admin/authenticate', ['class' => 'login-form']) ?>
                    <?= csrf_field() ?>
                    
                    <div class="form-floating">
                        <input type="text" 
                               class="form-control <?= (isset($validation) && $validation->hasError('identifier')) ? 'is-invalid' : '' ?>" 
                               id="identifier" 
                               name="identifier" 
                               placeholder="Username or Email"
                               value="<?= old('identifier') ?>"
                               required>
                        <label for="identifier">Username or Email</label>
                        <?php if (isset($validation) && $validation->hasError('identifier')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('identifier') ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="form-floating">
                        <input type="password" 
                               class="form-control <?= (isset($validation) && $validation->hasError('password')) ? 'is-invalid' : '' ?>" 
                               id="password" 
                               name="password" 
                               placeholder="Password"
                               required>
                        <label for="password">Password</label>
                        <?php if (isset($validation) && $validation->hasError('password')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('password') ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="material-icons me-2">login</i>
                        Sign In
                    </button>
                <?= form_close() ?>

                <!-- Forgot Password Link -->
                <div class="text-center">
                    <a href="<?= base_url('/admin/forgot-password') ?>" class="forgot-password">
                        <i class="material-icons me-1">help_outline</i>
                        Forgot your password?
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Material Design Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>

    <script>
        // Auto-focus on first input
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.getElementById('identifier');
            if (firstInput) {
                firstInput.focus();
            }
        });

        // Form validation feedback
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const identifier = document.getElementById('identifier').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!identifier || !password) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }
        });
    </script>
</body>
</html>
