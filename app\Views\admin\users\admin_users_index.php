<!-- <PERSON>er -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">User Management</h1>
        <p class="text-muted mb-0">Manage admin users and their permissions</p>
    </div>
    <div>
        <a href="<?= base_url('/admin/users/create') ?>" class="btn btn-primary">
            <i class="material-icons me-2">person_add</i>
            Create New User
        </a>
    </div>
</div>



<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="material-icons me-2">people</i>
            Admin Users
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="usersTable" class="table table-hover">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Contact</th>
                        <th>Role</th>
                        <th>Agency Type</th>
                        <th>Agency Name</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($adminUsers as $user): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></div>
                                    <small class="text-muted">@<?= esc($user['username']) ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <div class="text-sm"><?= esc($user['email']) ?></div>
                                <?php if (!empty($user['phone_number'])): ?>
                                <small class="text-muted"><?= esc($user['phone_number']) ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'supervisor' ? 'warning' : 'info') ?>">
                                <?= ucfirst(esc($user['role'])) ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($user['agency_id']): ?>
                                <span class="badge bg-secondary">Agency User</span>
                            <?php else: ?>
                                <span class="badge bg-primary">Admin Portal</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($user['agency_id'] && !empty($user['agency_name'])): ?>
                                <div class="fw-bold"><?= esc($user['agency_name']) ?></div>
                                <small class="text-muted"><?= esc($user['agency_code']) ?></small>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                        </td>
                        <td>
                            <?= date('M j, Y', strtotime($user['created_at'])) ?>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?= base_url('/admin/users/' . $user['id']) ?>"
                                   class="btn btn-sm btn-outline-primary"
                                   title="View User">
                                    <i class="material-icons">visibility</i>
                                </a>
                                <?php if ($user['role'] !== 'admin'): ?>
                                <a href="<?= base_url('/admin/users/' . $user['id'] . '/edit') ?>"
                                   class="btn btn-sm btn-outline-secondary"
                                   title="Edit User">
                                    <i class="material-icons">edit</i>
                                </a>
                                <?php else: ?>
                                <button type="button"
                                        class="btn btn-sm btn-outline-secondary"
                                        title="Admin users cannot be edited"
                                        disabled>
                                    <i class="material-icons">edit</i>
                                </button>
                                <?php endif; ?>
                                <?php if ($user['id'] != $currentUser['id'] && $user['role'] !== 'admin'): ?>
                                <button type="button"
                                        class="btn btn-sm btn-outline-danger"
                                        title="Delete User"
                                        onclick="confirmDelete(<?= $user['id'] ?>, '<?= esc($user['first_name'] . ' ' . $user['last_name']) ?>')">
                                    <i class="material-icons">delete</i>
                                </button>
                                <?php elseif ($user['role'] === 'admin'): ?>
                                <button type="button"
                                        class="btn btn-sm btn-outline-danger"
                                        title="Admin users cannot be deleted"
                                        disabled>
                                    <i class="material-icons">delete</i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="material-icons me-2">warning</i>
                    Confirm Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons me-1">delete</i>
                        Delete User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #1A4E8C 0%, #2563EB 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.text-sm {
    font-size: 0.875rem;
}

.display-1 {
    font-size: 6rem;
}
</style>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = '<?= base_url('/admin/users/') ?>' + userId + '/delete';

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
