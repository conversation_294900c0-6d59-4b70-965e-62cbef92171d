<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Create Agency</h1>
        <p class="text-muted mb-0">Add a new agency to the system</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/agencies') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to List
        </a>
    </div>
</div>

<!-- Create Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-building-add me-2"></i>
                    Agency Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('dakoii/agencies/store') ?>">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <!-- Agency Code -->
                        <div class="col-md-6 mb-3">
                            <label for="agency_code" class="form-label">
                                Agency Code <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('agency_code')) ? 'is-invalid' : '' ?>" 
                                   id="agency_code" 
                                   name="agency_code" 
                                   value="<?= old('agency_code') ?>" 
                                   style="text-transform: uppercase;"
                                   required>
                            <div class="form-text">Unique code for the agency (e.g., DOH001)</div>
                            <?php if (isset($validation) && $validation->hasError('agency_code')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('agency_code') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">
                                Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= (isset($validation) && $validation->hasError('status')) ? 'is-invalid' : '' ?>" 
                                    id="status" 
                                    name="status" 
                                    required>
                                <option value="">Select Status</option>
                                <?php foreach ($statusOptions as $value => $label): ?>
                                    <option value="<?= esc($value) ?>" <?= (old('status', 'active') === $value) ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (isset($validation) && $validation->hasError('status')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('status') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Agency Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            Agency Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control <?= (isset($validation) && $validation->hasError('name')) ? 'is-invalid' : '' ?>" 
                               id="name" 
                               name="name" 
                               value="<?= old('name') ?>" 
                               required>
                        <?php if (isset($validation) && $validation->hasError('name')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('name') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Address -->
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control <?= (isset($validation) && $validation->hasError('address')) ? 'is-invalid' : '' ?>" 
                                  id="address" 
                                  name="address" 
                                  rows="3"
                                  placeholder="Enter agency address..."><?= old('address') ?></textarea>
                        <?php if (isset($validation) && $validation->hasError('address')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('address') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="row">
                        <!-- Phone -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('phone')) ? 'is-invalid' : '' ?>" 
                                   id="phone" 
                                   name="phone" 
                                   value="<?= old('phone') ?>" 
                                   placeholder="+************">
                            <?php if (isset($validation) && $validation->hasError('phone')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('phone') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('email')) ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= old('email') ?>" 
                                   placeholder="<EMAIL>">
                            <?php if (isset($validation) && $validation->hasError('email')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('email') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="<?= base_url('dakoii/agencies') ?>" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            Create Agency
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Sidebar -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Help & Guidelines
                </h6>
            </div>
            <div class="card-body">
                <h6 class="text-primary">Agency Code Guidelines:</h6>
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <strong>Format:</strong> 3-6 letters + 3 numbers
                    </li>
                    <li class="mb-2">
                        <strong>Examples:</strong> DOH001, MOE002, DPM003
                    </li>
                    <li class="mb-2">
                        <strong>Rules:</strong> Must be unique across all agencies
                    </li>
                </ul>
                
                <hr>
                
                <h6 class="text-primary">Status Descriptions:</h6>
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <strong class="text-success">Active:</strong> Fully operational agency
                    </li>
                    <li class="mb-2">
                        <strong class="text-warning">Inactive:</strong> Temporarily not operational
                    </li>
                    <li class="mb-2">
                        <strong class="text-danger">Suspended:</strong> Operations suspended
                    </li>
                </ul>
                
                <hr>
                
                <div class="alert alert-info">
                    <i class="bi bi-lightbulb me-2"></i>
                    <strong>Tip:</strong> Agency codes are automatically converted to uppercase for consistency.
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="generateCode()">
                        <i class="bi bi-magic me-2"></i>
                        Generate Code
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearForm()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        Clear Form
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateCode() {
    const name = document.getElementById('name').value;
    if (name.length >= 3) {
        const baseCode = name.replace(/[^A-Za-z]/g, '').substring(0, 3).toUpperCase();
        const randomNum = Math.floor(Math.random() * 900) + 100;
        document.getElementById('agency_code').value = baseCode + randomNum;
    } else {
        alert('Please enter agency name first to generate code.');
    }
}

function clearForm() {
    if (confirm('Are you sure you want to clear all form data?')) {
        document.querySelector('form').reset();
    }
}

// Auto-uppercase agency code
document.getElementById('agency_code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});
</script>
