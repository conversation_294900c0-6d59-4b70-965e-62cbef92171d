<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Libraries\AdminAuth;

class AdminAuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $auth = new AdminAuth();

        // Check if user is authenticated and session is valid
        if (!$auth->validateSession()) {
            // Store intended URL for redirect after login
            $session = \Config\Services::session();
            $session->set('admin_intended_url', current_url());

            // Redirect to admin login page
            return redirect()->to('/admin/login')->with('error', 'Please log in to access this page.');
        }

        // Check permissions if arguments are provided
        if (!empty($arguments)) {
            $permission = $arguments[0] ?? '';
            $resource = $arguments[1] ?? '';

            if (!$auth->hasPermission($permission, $resource)) {
                // User doesn't have permission
                return redirect()->back()->with('error', 'You do not have permission to access this resource.');
            }
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do here
    }
}
