<!-- Dashboard Overview Cards -->
<div class="row mb-4">
    <!-- Total Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['total_users']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="material-icons text-primary" style="font-size: 2rem;">people</i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['active_users']) ?>
                        </div>
                        <?php if ($stats['user_growth_percentage'] != 0): ?>
                        <div class="text-xs">
                            <span class="<?= $stats['user_growth_percentage'] > 0 ? 'text-success' : 'text-danger' ?>">
                                <i class="material-icons" style="font-size: 12px;">
                                    <?= $stats['user_growth_percentage'] > 0 ? 'trending_up' : 'trending_down' ?>
                                </i>
                                <?= abs($stats['user_growth_percentage']) ?>%
                            </span>
                            <span class="text-muted">vs last month</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <i class="material-icons text-success" style="font-size: 2rem;">person_check</i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Agencies Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Agencies
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['total_agencies']) ?>
                        </div>
                        <div class="text-xs text-muted">
                            <?= number_format($stats['active_agencies']) ?> active
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="material-icons text-info" style="font-size: 2rem;">business</i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Portal Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Admin Portal Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['admin_portal_users']) ?>
                        </div>
                        <div class="text-xs text-muted">
                            <?= number_format($stats['agency_portal_users']) ?> agency users
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="material-icons text-warning" style="font-size: 2rem;">admin_panel_settings</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Users by Role Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Users by Role</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="userRoleChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="mr-2">
                        <i class="material-icons text-primary">circle</i> Admin (<?= $usersByRole['admin'] ?>)
                    </span>
                    <span class="mr-2">
                        <i class="material-icons text-success">circle</i> Supervisor (<?= $usersByRole['supervisor'] ?>)
                    </span>
                    <span class="mr-2">
                        <i class="material-icons text-info">circle</i> User (<?= $usersByRole['user'] ?>)
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Users -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Users</h6>
                <a href="<?= base_url('/admin/users') ?>" class="btn btn-primary btn-sm">
                    <i class="material-icons me-1">people</i>
                    View All Users
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($recentUsers)): ?>
                                <?php foreach ($recentUsers as $user): ?>
                                <tr>
                                    <td><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></td>
                                    <td><?= esc($user['email']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'supervisor' ? 'warning' : 'info') ?>">
                                            <?= ucfirst(esc($user['role'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                            <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td><?= date('M j, Y', strtotime($user['created_at'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No users found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Agency Statistics -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Top Agencies by User Count</h6>
                <a href="<?= base_url('/admin/agency-users') ?>" class="btn btn-primary btn-sm">
                    <i class="material-icons me-1">business</i>
                    Manage Agency Users
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Agency Code</th>
                                <th>Agency Name</th>
                                <th>Status</th>
                                <th>User Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($agencyStats)): ?>
                                <?php foreach ($agencyStats as $agency): ?>
                                <tr>
                                    <td><strong><?= esc($agency['agency_code']) ?></strong></td>
                                    <td><?= esc($agency['name']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $agency['status'] === 'active' ? 'success' : ($agency['status'] === 'inactive' ? 'secondary' : 'warning') ?>">
                                            <?= ucfirst(esc($agency['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= number_format($agency['user_count']) ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('/admin/agency-users?agency=' . $agency['id']) ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="material-icons">visibility</i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No agencies found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('/admin/users/create') ?>" class="btn btn-primary btn-block">
                            <i class="material-icons me-2">person_add</i>
                            Add New User
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('/admin/users') ?>" class="btn btn-success btn-block">
                            <i class="material-icons me-2">people</i>
                            Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('/admin/agency-users') ?>" class="btn btn-info btn-block">
                            <i class="material-icons me-2">business</i>
                            Agency Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('/admin/reports') ?>" class="btn btn-warning btn-block">
                            <i class="material-icons me-2">assessment</i>
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Users by Role Pie Chart
const ctx = document.getElementById('userRoleChart').getContext('2d');
const userRoleChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Admin', 'Supervisor', 'User'],
        datasets: [{
            data: [<?= $usersByRole['admin'] ?>, <?= $usersByRole['supervisor'] ?>, <?= $usersByRole['user'] ?>],
            backgroundColor: ['#1A4E8C', '#28a745', '#17a2b8'],
            hoverBackgroundColor: ['#164a87', '#218838', '#138496'],
            borderWidth: 2,
            borderColor: '#ffffff'
        }]
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        cutout: '70%'
    }
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #1A4E8C !important;
}

.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}

.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

.text-xs {
    font-size: 0.75rem;
}

.btn-block {
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
}
</style>
