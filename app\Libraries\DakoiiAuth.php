<?php

namespace App\Libraries;

use App\Models\DakoiiUserModel;
use CodeIgniter\Session\Session;

class DakoiiAuth
{
    protected $session;
    protected $userModel;

    public function __construct()
    {
        $this->session = \Config\Services::session();
        $this->userModel = new DakoiiUserModel();
    }

    /**
     * Attempt to log in a user
     */
    public function attempt(string $identifier, string $password): bool
    {
        $user = $this->userModel->findByUsernameOrEmail($identifier);

        if (!$user) {
            return false;
        }

        if (!$this->userModel->verifyPassword($password, $user['password_hash'])) {
            return false;
        }

        if (!$user['is_active']) {
            return false;
        }

        // Set session data
        $this->session->set([
            'dakoii_user_id' => $user['id'],
            'dakoii_username' => $user['username'],
            'dakoii_email' => $user['email'],
            'dakoii_first_name' => $user['first_name'],
            'dakoii_last_name' => $user['last_name'],
            'dakoii_logged_in' => true
        ]);

        // Update last login
        $this->userModel->updateLastLogin($user['id']);

        return true;
    }

    /**
     * Check if user is logged in
     */
    public function check(): bool
    {
        return $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user data
     */
    public function user(): ?array
    {
        if (!$this->check()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'username' => $this->session->get('dakoii_username'),
            'email' => $this->session->get('dakoii_email'),
            'first_name' => $this->session->get('dakoii_first_name'),
            'last_name' => $this->session->get('dakoii_last_name'),
        ];
    }

    /**
     * Get current user ID
     */
    public function id(): ?int
    {
        return $this->check() ? $this->session->get('dakoii_user_id') : null;
    }

    /**
     * Get current user's full name
     */
    public function fullName(): string
    {
        if (!$this->check()) {
            return '';
        }

        return $this->session->get('dakoii_first_name') . ' ' . $this->session->get('dakoii_last_name');
    }

    /**
     * Log out the user
     */
    public function logout(): void
    {
        $this->session->remove([
            'dakoii_user_id',
            'dakoii_username',
            'dakoii_email',
            'dakoii_first_name',
            'dakoii_last_name',
            'dakoii_logged_in'
        ]);

        $this->session->regenerate(true);
    }

    /**
     * Generate remember token (for future use)
     */
    public function generateRememberToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Validate session timeout
     */
    public function validateSession(): bool
    {
        if (!$this->check()) {
            return false;
        }

        $lastActivity = $this->session->get('dakoii_last_activity');
        $timeout = 1800; // 30 minutes

        if ($lastActivity && (time() - $lastActivity > $timeout)) {
            $this->logout();
            return false;
        }

        $this->session->set('dakoii_last_activity', time());
        return true;
    }
}
