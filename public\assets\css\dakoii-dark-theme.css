/* Dakoii Portal Dark Theme */
/* CHS Brand Colors:
   - Deep Cerulean Blue: #1A4E8C
   - Engine Red: #C8102E
   - White: #FFFFFF
   - Black: #000000
*/

:root {
    --dakoii-primary: #1A4E8C;
    --dakoii-danger: #C8102E;
    --dakoii-dark: #1a1a1a;
    --dakoii-dark-secondary: #2d3748;
    --dakoii-dark-tertiary: #374151;
    --dakoii-dark-quaternary: #4a5568;
    --dakoii-light: #f7fafc;
    --dakoii-light-secondary: #e2e8f0;
    --dakoii-success: #10b981;
    --dakoii-warning: #f59e0b;
    --dakoii-info: #3b82f6;
}

/* Global Dark Theme */
body {
    background-color: var(--dakoii-dark);
    color: var(--dakoii-light);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Bootstrap Overrides */
.bg-dark {
    background-color: var(--dakoii-dark-secondary) !important;
}

.bg-primary {
    background-color: var(--dakoii-primary) !important;
}

.bg-danger {
    background-color: var(--dakoii-danger) !important;
}

.text-light {
    color: var(--dakoii-light) !important;
}

.text-primary {
    color: var(--dakoii-primary) !important;
}

/* Cards */
.card {
    background-color: var(--dakoii-dark-secondary);
    border: 1px solid var(--dakoii-dark-quaternary);
    color: var(--dakoii-light);
}

.card-header {
    background-color: var(--dakoii-dark-tertiary);
    border-bottom: 1px solid var(--dakoii-dark-quaternary);
    color: var(--dakoii-light);
}

/* Forms */
.form-control {
    background-color: var(--dakoii-dark-tertiary);
    border: 1px solid var(--dakoii-dark-quaternary);
    color: var(--dakoii-light);
}

.form-control:focus {
    background-color: var(--dakoii-dark-tertiary);
    border-color: var(--dakoii-primary);
    box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
    color: var(--dakoii-light);
}

.form-control::placeholder {
    color: var(--dakoii-light-secondary);
}

.form-label {
    color: var(--dakoii-light);
    font-weight: 500;
}

/* Buttons */
.btn-primary {
    background-color: var(--dakoii-primary);
    border-color: var(--dakoii-primary);
}

.btn-primary:hover {
    background-color: #164080;
    border-color: #164080;
}

.btn-danger {
    background-color: var(--dakoii-danger);
    border-color: var(--dakoii-danger);
}

.btn-danger:hover {
    background-color: #a00d26;
    border-color: #a00d26;
}

/* Sidebar */
.sidebar {
    background-color: var(--dakoii-dark-secondary);
    border-right: 1px solid var(--dakoii-dark-quaternary);
    min-height: 100vh;
    width: 250px;
    flex-shrink: 0;
}

.sidebar .nav-link {
    color: var(--dakoii-light-secondary);
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
}

.sidebar .nav-link:hover {
    background-color: var(--dakoii-dark-tertiary);
    color: var(--dakoii-light);
}

.sidebar .nav-link.active {
    background-color: var(--dakoii-primary);
    color: white;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1.25rem;
}

/* Top Navigation */
.navbar-dark {
    background-color: var(--dakoii-dark-secondary) !important;
    border-bottom: 1px solid var(--dakoii-dark-quaternary);
}

.navbar-dark .navbar-brand {
    color: var(--dakoii-light);
    font-weight: 600;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--dakoii-light-secondary);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--dakoii-light);
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--dakoii-dark-secondary);
    border: 1px solid var(--dakoii-dark-quaternary);
}

.dropdown-item {
    color: var(--dakoii-light-secondary);
}

.dropdown-item:hover {
    background-color: var(--dakoii-dark-tertiary);
    color: var(--dakoii-light);
}

/* Tables */
.table-dark {
    --bs-table-bg: var(--dakoii-dark-secondary);
    --bs-table-striped-bg: var(--dakoii-dark-tertiary);
    --bs-table-border-color: var(--dakoii-dark-quaternary);
}

/* Alerts */
.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: var(--dakoii-success);
    color: var(--dakoii-success);
}

.alert-danger {
    background-color: rgba(200, 16, 46, 0.1);
    border-color: var(--dakoii-danger);
    color: var(--dakoii-danger);
}

.alert-info {
    background-color: rgba(26, 78, 140, 0.1);
    border-color: var(--dakoii-primary);
    color: var(--dakoii-primary);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--dakoii-warning);
    color: var(--dakoii-warning);
}

/* Login Page Specific */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--dakoii-dark) 0%, var(--dakoii-dark-secondary) 100%);
}

.login-card {
    background-color: var(--dakoii-dark-secondary);
    border: 1px solid var(--dakoii-dark-quaternary);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.login-logo {
    max-width: 120px;
    height: auto;
}

/* Dashboard Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--dakoii-dark-secondary) 0%, var(--dakoii-dark-tertiary) 100%);
    border: 1px solid var(--dakoii-dark-quaternary);
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Breadcrumbs */
.breadcrumb {
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--dakoii-primary);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--dakoii-light-secondary);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dakoii-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--dakoii-dark-quaternary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dakoii-primary);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease-in-out;
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        width: 100% !important;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        display: none;
    }
}
