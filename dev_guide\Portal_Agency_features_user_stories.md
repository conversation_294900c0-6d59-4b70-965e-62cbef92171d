# Agency Portal - System Features & User Stories

## System Overview
The Agency Portal serves as the health facility management interface for the CHS PNG employee management system, providing comprehensive employee management, onboarding, and document verification capabilities for individual health facilities and agencies.

---

## 🎯 Core System Features

### 1. **Authentication & Access Control**
- Shared login form with Admin Portal (unified authentication)
- Authentication using `admin_users` table with agency_id populated
- Login routing based on agency_id (populated = Agency Portal dashboard)
- Session management with agency-specific privileges
- Role-based access control within agency context

### 2. **Professional Agency Theme Interface**
- Clean, professional template based on landing page theme
- Agency-specific branding and customization
- CHS branding consistency with agency identity
- Responsive design for desktop and mobile
- Modern UI components with accessibility compliance
- Light theme with agency-customizable accent colors

### 3. **Employee Management (CRUD)**
- Complete employee lifecycle management
- Employee registration and onboarding process
- Employee profile management and updates
- Employee status tracking and reporting
- Document management and verification

### 4. **Employee Onboarding Process**
- Structured onboarding workflow based on CHS Bio-Data forms
- Document collection and verification checklist
- Pre-vetting process management
- Employment contract processing
- Professional qualification verification

### 5. **Document Verification System**
- Requirements checklist management (based on CHS documentation)
- Document upload and status tracking
- Approval workflow for document verification
- Compliance monitoring and reporting

### 6. **Employee Self-Service via Public Links**
- Public profile links for employee self-service updates
- Secure token-based access for employee profile management
- Link expiration logic after profile assessment and submission
- Dual upload capability for both agency users and employees
- Employee-initiated document uploads and information updates

### 7. **Agency-Specific Operations**
- Agency dashboard with facility-specific metrics
- Employee distribution and statistics
- Payroll preparation and coordination
- Performance tracking and evaluation

---

## 👤 User Stories

### **Authentication & Access Control**

#### Story #1: Agency Portal Login Access
**As an** Agency Portal user  
**I want to** access the agency portal through the shared login system  
**So that** I can manage employees specific to my health facility  

**Acceptance Criteria:**
- Use shared login form with Admin Portal
- Authenticate using credentials from `admin_users` table
- System checks agency_id field after successful authentication
- If agency_id is populated, redirect to Agency Portal dashboard
- Display agency-specific welcome message and branding
- Show appropriate error messages for users without agency assignment
- Support password reset through shared authentication system

#### Story #2: Agency-Specific Access Control
**As an** Agency Portal user  
**I want to** have access restricted to my assigned agency's data only  
**So that** I can only manage employees and data relevant to my facility  

**Acceptance Criteria:**
- Filter all employee data by user's assigned agency_id
- Prevent access to other agencies' employee information
- Display agency name and details in portal header
- Role-based permissions within agency context
- Audit trail for all agency-specific actions

---

### **Dashboard & Overview**

#### Story #3: Agency Dashboard Overview
**As an** Agency Portal user  
**I want to** see a comprehensive agency dashboard upon login  
**So that** I can quickly understand my facility's employee status and operations  

**Acceptance Criteria:**
- Display total number of employees in the agency
- Show employee status distribution (active, inactive, pending)
- Display recent employee activities and registrations
- Show pending document verifications and approvals
- Quick access buttons to common employee management functions
- Agency-specific metrics and performance indicators

#### Story #4: Employee Statistics Widgets
**As an** Agency user  
**I want to** see key employee metrics and alerts on the dashboard  
**So that** I can monitor staffing levels and compliance requirements  

**Acceptance Criteria:**
- Widget showing employees by department/role distribution
- Alert notifications for incomplete employee documentation
- Recent activity feed for employee management actions
- Statistics on employee qualifications and certifications
- Compliance status indicators for regulatory requirements
- Links to detailed employee reports and management functions

---

### **Employee Management (CRUD Operations)**

#### Story #5: Create New Employee Profile
**As an** Agency Portal user  
**I want to** register new employees in the system  
**So that** I can initiate the onboarding process for new hires  

**Acceptance Criteria:**
- Create employee profile with bio-data information (following CHS Bio-Data Form structure)
- Capture personal information (name, NID, contact details, emergency contacts)
- Record professional qualifications and employment history
- Set initial employee status (pending, active, inactive)
- Generate unique employee file number
- Link employee automatically to current user's agency
- Send welcome email to new employee with portal access details

#### Story #6: Employee Onboarding Workflow
**As an** Agency Portal user  
**I want to** guide new employees through a structured onboarding process  
**So that** all required documentation and procedures are completed systematically  

**Acceptance Criteria:**
- Display onboarding checklist based on CHS requirements
- Track completion of each onboarding step
- Manage document submission and verification process
- Set up employee banking details (primary account, NASFUND)
- Complete employment contract processing
- Generate onboarding progress reports
- Send notifications for incomplete onboarding steps

#### Story #7: View Employee Directory
**As an** Agency Portal user  
**I want to** view a comprehensive list of all employees in my agency  
**So that** I can access and manage employee information efficiently  

**Acceptance Criteria:**
- Display paginated list of agency employees
- Show key employee details (name, position, status, employee ID)
- Search functionality by name, employee ID, or position
- Filter options by status, department, or qualification
- Sort options by name, hire date, position
- Quick action buttons (view profile, edit, manage documents)
- Export employee list to Excel/PDF formats

#### Story #8: Edit Employee Information
**As an** Agency Portal user  
**I want to** update existing employee details  
**So that** employee records remain current and accurate  

**Acceptance Criteria:**
- Edit employee personal information (contact details, emergency contacts)
- Update professional qualifications and certifications
- Modify employment details (position, department, status)
- Update banking and NASFUND information
- Change employee status (activate, deactivate, suspend)
- Log all changes with timestamp and modifier information
- Send notifications for significant profile changes

#### Story #9: Employee Profile Management
**As an** Agency Portal user  
**I want to** access detailed employee profiles  
**So that** I can view complete employee information and history  

**Acceptance Criteria:**
- Display comprehensive employee profile with all bio-data
- Show employment history and position changes
- View qualification details and professional memberships
- Access document verification status and history
- Display performance reviews and evaluations
- Show attendance and leave records (if applicable)
- Print-friendly employee profile format

---

### **Employee Self-Service via Public Links**

#### Story #17: Generate Employee Self-Service Link
**As an** Agency Portal user
**I want to** generate secure public profile links for employees
**So that** employees can update their own information and upload documents

**Acceptance Criteria:**
- Generate unique, secure token-based public links when employee is created with email
- Link includes employee identification and secure access token
- Send email notification to employee with their personal profile link
- Link provides access to employee's own profile information only
- Display clear instructions for employees on how to use the link
- Track link generation and access attempts for security audit
- Ensure link works without requiring employee to create account or login

#### Story #18: Employee Self-Service Profile Updates
**As an** Employee with a public profile link
**I want to** update my personal information through the secure link
**So that** I can keep my employment records current without contacting HR

**Acceptance Criteria:**
- Access personal profile using secure public link
- Update personal information (contact details, emergency contacts, address)
- Upload required documents (certificates, ID copies, medical certificates)
- View current document submission status and requirements
- Submit updated information for agency review and approval
- Receive confirmation notifications for successful submissions
- User-friendly interface optimized for mobile and desktop access

#### Story #19: Link Expiration Management
**As an** Agency Portal user
**I want to** manage the expiration of employee self-service links
**So that** security is maintained and links don't remain active indefinitely

**Acceptance Criteria:**
- Links automatically expire after employee profile is assessed and submitted
- Manual link expiration capability for agency users
- Generate new links for employees when needed
- Clear notification to employees when their link expires
- Track link usage and expiration status in employee records
- Option to set custom expiration timeframes for different scenarios
- Expired links display appropriate message with contact information

#### Story #20: Dual Upload Capability
**As an** Agency Portal user or Employee
**I want to** upload documents and information from either interface
**So that** document collection is flexible and efficient

**Acceptance Criteria:**
- Agency users can upload documents on behalf of employees
- Employees can upload documents through their public profile links
- Both upload methods update the same document tracking system
- Clear indication of who uploaded each document (agency vs employee)
- Version control for documents uploaded from multiple sources
- Notification system for new uploads from either source
- Consistent document verification workflow regardless of upload source

#### Story #21: Employee Document Self-Upload
**As an** Employee with a public profile link
**I want to** upload my required documents directly
**So that** I can complete my onboarding requirements independently

**Acceptance Criteria:**
- View checklist of required documents with clear descriptions
- Upload multiple document types (PDF, images, scanned documents)
- File size and format validation with user-friendly error messages
- Progress tracking showing completed vs pending document requirements
- Ability to replace or update previously uploaded documents
- Immediate confirmation of successful document uploads
- Mobile-optimized upload interface for smartphone users

---

### **Document Management & Verification**

#### Story #10: Manage Employee Documents
**As an** Agency Portal user  
**I want to** manage and track employee documentation  
**So that** all required documents are collected and verified  

**Acceptance Criteria:**
- Display document checklist based on CHS requirements (Bio-data form, contracts, credentials, etc.)
- Upload and store employee documents securely
- Track document submission status and verification
- Set document expiration dates and renewal reminders
- Approve or reject submitted documents with comments
- Generate document compliance reports
- Send notifications for missing or expiring documents

#### Story #11: Document Verification Workflow
**As an** Agency Portal user  
**I want to** verify employee documents systematically  
**So that** compliance requirements are met and maintained  

**Acceptance Criteria:**
- Review submitted documents against requirements checklist
- Mark documents as verified, pending, or rejected
- Add verification comments and notes
- Set up approval workflow for sensitive documents
- Track verification history and audit trail
- Generate verification completion certificates
- Notify employees of document status changes

#### Story #12: Pre-vetting Checklist Management
**As an** Agency Portal user  
**I want to** manage the employee pre-vetting process  
**So that** all new hires meet CHS employment standards  

**Acceptance Criteria:**
- Display pre-vetting checklist (medical certificate, police clearance, etc.)
- Track completion of each pre-vetting requirement
- Manage approval workflow for pre-vetting items
- Generate pre-vetting completion reports
- Set up reminders for pending pre-vetting items
- Integration with external verification systems where possible
- Final pre-vetting approval and certification

---

### **Employment Contract & Payroll**

#### Story #13: Employment Contract Management
**As an** Agency Portal user  
**I want to** manage employee contracts and employment terms  
**So that** legal and administrative requirements are properly handled  

**Acceptance Criteria:**
- Create and manage employment contracts
- Track contract status and renewal dates
- Store signed contract documents
- Manage contract amendments and updates
- Generate contract renewal notifications
- Link contracts to employee profiles
- Maintain contract history and versions

#### Story #14: Payroll Coordination
**As an** Agency Portal user  
**I want to** coordinate employee payroll information  
**So that** employees can be processed for payment accurately  

**Acceptance Criteria:**
- Manage employee banking details for payroll
- Track NASFUND membership and contributions
- Coordinate with IRC salary and wages declarations
- Generate payroll-ready employee data
- Manage tax information and deductions
- Export payroll data in required formats
- Integration with external payroll systems

---

### **Employee Performance & Development**

#### Story #15: Employee Status Management
**As an** Agency Portal user  
**I want to** manage employee status and performance tracking  
**So that** I can monitor and improve employee performance  

**Acceptance Criteria:**
- Update employee status (active, inactive, suspended, terminated)
- Track employee performance metrics and evaluations
- Manage disciplinary actions and improvements
- Record training and development activities
- Set employee goals and objectives
- Generate performance reports and analytics
- Schedule performance review reminders

#### Story #16: Professional Development Tracking
**As an** Agency Portal user  
**I want to** track employee professional development and qualifications  
**So that** staff capabilities and compliance are maintained  

**Acceptance Criteria:**
- Record additional qualifications and certifications
- Track continuing education and training completion
- Manage professional license renewals
- Monitor compliance with professional requirements
- Generate professional development reports
- Set up training and development schedules
- Integration with professional bodies and certification systems

---

## 🗄️ Database Schema Requirements

### CodeIgniter 4 Employee Model Structure

#### EmployeeModel (new table)
```php
<?php
// app/Models/EmployeeModel.php

namespace App\Models;
use CodeIgniter\Model;

class EmployeeModel extends Model
{
    protected $table = 'employees';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'employee_number', 'agency_id', 'nid_number', 'position_number',
        'first_name', 'middle_name', 'last_name', 'gender', 'date_of_birth',
        'date_of_commencement', 'marital_status', 'spouse_name', 'number_of_children',
        'home_province', 'home_district', 'home_village', 'mobile_number', 'email_address',
        'emergency_contact_person', 'emergency_contact_phone',
        'designation', 'department', 'employment_status', 'is_active'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'employee_number' => 'required|is_unique[employees.employee_number]',
        'agency_id' => 'required|integer',
        'first_name' => 'required|min_length[2]|max_length[50]',
        'last_name' => 'required|min_length[2]|max_length[50]',
        'email_address' => 'permit_empty|valid_email',
        'mobile_number' => 'required|min_length[10]|max_length[20]',
        'gender' => 'required|in_list[male,female]',
        'employment_status' => 'required|in_list[active,inactive,pending,terminated]'
    ];
    
    // Relationship with agency
    public function getAgency($employee_id)
    {
        return $this->join('agencies', 'agencies.id = employees.agency_id')
                    ->where('employees.id', $employee_id)
                    ->first();
    }
}
```

#### employees Table Migration
```php
<?php
// app/Database/Migrations/YYYY-MM-DD-HHMMSS_CreateEmployeesTable.php

class CreateEmployeesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'employee_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'unique' => true,
            ],
            'agency_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'nid_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'unique' => true,
                'null' => true,
            ],
            'position_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            // Personal Information
            'first_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'middle_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'last_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'gender' => [
                'type' => 'ENUM',
                'constraint' => ['male', 'female'],
            ],
            'date_of_birth' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'date_of_commencement' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'marital_status' => [
                'type' => 'ENUM',
                'constraint' => ['single', 'married', 'divorced', 'widowed'],
                'null' => true,
            ],
            'spouse_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'number_of_children' => [
                'type' => 'INT',
                'constraint' => 3,
                'default' => 0,
            ],
            // Contact Information
            'home_province' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'home_district' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'home_village' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'mobile_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
            ],
            'email_address' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'emergency_contact_person' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'emergency_contact_phone' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            // Employment Information
            'designation' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'department' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'employment_status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive', 'pending', 'terminated'],
                'default' => 'pending',
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
            ],
            // Timestamps
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'updated_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('employee_number');
        $this->forge->addIndex('agency_id');
        $this->forge->addForeignKey('agency_id', 'agencies', 'id', 'RESTRICT', 'CASCADE');
        $this->forge->createTable('employees');
    }

    public function down()
    {
        $this->forge->dropTable('employees');
    }
}
```

#### employee_qualifications Table Migration
```php
<?php
// For storing employee qualifications and professional information

class CreateEmployeeQualificationsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => ['type' => 'INT', 'auto_increment' => true],
            'employee_id' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true],
            'qualification_type' => ['type' => 'ENUM', 'constraint' => ['basic', 'additional']],
            'qualification_name' => ['type' => 'VARCHAR', 'constraint' => 255],
            'institution' => ['type' => 'VARCHAR', 'constraint' => 255, 'null' => true],
            'course_duration' => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true],
            'completion_year' => ['type' => 'YEAR', 'null' => true],
            'certificate_number' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true],
            'is_verified' => ['type' => 'TINYINT', 'constraint' => 1, 'default' => 0],
            'created_at' => ['type' => 'DATETIME', 'null' => true],
            'updated_at' => ['type' => 'DATETIME', 'null' => true],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addIndex('employee_id');
        $this->forge->addForeignKey('employee_id', 'employees', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('employee_qualifications');
    }
}
```

#### employee_documents Table Migration
```php
<?php
// For tracking employee document submission and verification

class CreateEmployeeDocumentsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => ['type' => 'INT', 'auto_increment' => true],
            'employee_id' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true],
            'document_type' => ['type' => 'VARCHAR', 'constraint' => 100],
            'document_name' => ['type' => 'VARCHAR', 'constraint' => 255],
            'file_path' => ['type' => 'VARCHAR', 'constraint' => 500, 'null' => true],
            'status' => ['type' => 'ENUM', 'constraint' => ['pending', 'verified', 'rejected'], 'default' => 'pending'],
            'verified_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
            'verified_at' => ['type' => 'DATETIME', 'null' => true],
            'expiry_date' => ['type' => 'DATE', 'null' => true],
            'comments' => ['type' => 'TEXT', 'null' => true],
            'created_at' => ['type' => 'DATETIME', 'null' => true],
            'updated_at' => ['type' => 'DATETIME', 'null' => true],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addIndex(['employee_id', 'document_type']);
        $this->forge->addForeignKey('employee_id', 'employees', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('verified_by', 'admin_users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->createTable('employee_documents');
    }
}
```

#### employee_banking Table Migration
```php
<?php
// For storing employee banking and NASFUND information

class CreateEmployeeBankingTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => ['type' => 'INT', 'auto_increment' => true],
            'employee_id' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'unique' => true],
            'bank_name' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true],
            'bank_branch' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true],
            'account_name' => ['type' => 'VARCHAR', 'constraint' => 255, 'null' => true],
            'account_number' => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true],
            'account_type' => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true],
            'nasfund_member_name' => ['type' => 'VARCHAR', 'constraint' => 255, 'null' => true],
            'nasfund_membership_number' => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true],
            'nasfund_year_joined' => ['type' => 'YEAR', 'null' => true],
            'nasfund_branch' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true],
            'created_at' => ['type' => 'DATETIME', 'null' => true],
            'updated_at' => ['type' => 'DATETIME', 'null' => true],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('employee_id');
        $this->forge->addForeignKey('employee_id', 'employees', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('employee_banking');
    }
}
```

#### employee_profile_links Table Migration
```php
<?php
// For managing employee self-service public profile links

class CreateEmployeeProfileLinksTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'employee_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'unique' => true,
            ],
            'access_token' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'unique' => true,
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
            ],
            'expires_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'last_accessed_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'access_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'profile_submitted' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
            ],
            'submitted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('employee_id');
        $this->forge->addUniqueKey('access_token');
        $this->forge->addIndex('is_active');
        $this->forge->addIndex('expires_at');
        $this->forge->addForeignKey('employee_id', 'employees', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('created_by', 'admin_users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->createTable('employee_profile_links');
    }

    public function down()
    {
        $this->forge->dropTable('employee_profile_links');
    }
}
```

#### EmployeeProfileLinkModel
```php
<?php
// app/Models/EmployeeProfileLinkModel.php

namespace App\Models;
use CodeIgniter\Model;

class EmployeeProfileLinkModel extends Model
{
    protected $table = 'employee_profile_links';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'employee_id', 'access_token', 'is_active', 'expires_at',
        'last_accessed_at', 'access_count', 'profile_submitted',
        'submitted_at', 'created_by'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'employee_id' => 'required|integer|is_unique[employee_profile_links.employee_id]',
        'access_token' => 'required|min_length[32]|is_unique[employee_profile_links.access_token]',
    ];

    // Generate secure access token
    public function generateAccessToken()
    {
        return bin2hex(random_bytes(32));
    }

    // Check if link is valid and active
    public function isValidLink($token)
    {
        $link = $this->where('access_token', $token)
                     ->where('is_active', 1)
                     ->first();

        if (!$link) return false;

        // Check expiration
        if ($link['expires_at'] && strtotime($link['expires_at']) < time()) {
            return false;
        }

        // Check if profile already submitted
        if ($link['profile_submitted']) {
            return false;
        }

        return $link;
    }

    // Update access tracking
    public function trackAccess($token)
    {
        $this->where('access_token', $token)
             ->set([
                 'last_accessed_at' => date('Y-m-d H:i:s'),
                 'access_count' => 'access_count + 1'
             ], false)
             ->update();
    }

    // Mark profile as submitted and expire link
    public function markProfileSubmitted($token)
    {
        $this->where('access_token', $token)
             ->set([
                 'profile_submitted' => 1,
                 'submitted_at' => date('Y-m-d H:i:s'),
                 'is_active' => 0
             ])
             ->update();
    }
}
```

---

## 🎨 UI/UX Requirements

### Professional Agency Theme Specifications
- **Primary Colors**:
  - CHS Deep Blue (#1A4E8C) - Headers, primary buttons, navigation
  - CHS Red (#C8102E) - Accent elements, alerts, important actions
  - Agency Accent Color - Customizable per agency (default: Teal #20B2AA)
  - White (#FFFFFF) - Background, cards, content areas
  - Light Gray (#F8F9FA) - Secondary backgrounds, forms
  - Dark Gray (#343A40) - Text, icons

### Bootstrap 5 Agency Template
- **CSS Framework**: Bootstrap 5 with agency-specific customizations
- **Layout**: Professional card-based dashboard layout
- **Typography**: Clean, readable fonts with proper hierarchy
- **Components**: Light-themed components with agency branding
- **Icons**: Bootstrap Icons with medical/healthcare themed icons
- **Forms**: Professional form styling with validation feedback

### Agency Navigation Structure
```html
<!-- Agency Header with Branding -->
<header class="agency-header bg-primary text-white">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="mb-0">
                    <i class="bi bi-hospital"></i>
                    [Agency Name] - Employee Management
                </h4>
                <small class="text-light">[Agency Location]</small>
            </div>
            <div class="col-md-6 text-end">
                <!-- User Profile & Logout -->
            </div>
        </div>
    </div>
</header>

<!-- Navigation Tabs/Pills for Main Sections -->
<nav class="bg-light border-bottom">
    <div class="container-fluid">
        <ul class="nav nav-pills nav-fill">
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">Dashboard</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#employees">Employee Directory</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#onboarding">Onboarding</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#documents">Documents</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#reports">Reports</a>
            </li>
        </ul>
    </div>
</nav>
```

### Dashboard Widget Layout
```html
<!-- Agency Dashboard Widgets -->
<div class="row">
    <!-- Employee Statistics -->
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-people-fill fs-1 text-primary"></i>
                <h3 class="mt-2">[Total Employees]</h3>
                <p class="text-muted">Total Staff</p>
            </div>
        </div>
    </div>
    
    <!-- Active Employees -->
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-check-circle-fill fs-1 text-success"></i>
                <h3 class="mt-2">[Active Count]</h3>
                <p class="text-muted">Active Staff</p>
            </div>
        </div>
    </div>
    
    <!-- Pending Onboarding -->
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-hourglass-split fs-1 text-warning"></i>
                <h3 class="mt-2">[Pending Count]</h3>
                <p class="text-muted">Pending Onboarding</p>
            </div>
        </div>
    </div>
    
    <!-- Document Alerts -->
    <div class="col-md-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle-fill fs-1 text-danger"></i>
                <h3 class="mt-2">[Alert Count]</h3>
                <p class="text-muted">Document Alerts</p>
            </div>
        </div>
    </div>
</div>
```

### CodeIgniter 4 View Structure
```php
// app/Views/layouts/agency_layout.php - Agency-specific layout
// app/Views/agency/ - All Agency portal views
//   ├── dashboard/
//   │   └── index.php
//   ├── employees/
//   │   ├── index.php (directory)
//   │   ├── create.php (new employee)
//   │   ├── edit.php (edit profile)
//   │   ├── view.php (employee profile)
//   │   └── onboarding.php (onboarding process)
//   ├── documents/
//   │   ├── index.php (document management)
//   │   ├── checklist.php (requirements)
//   │   ├── upload.php (document upload)
//   │   └── verify.php (verification)
//   ├── reports/
//   │   ├── employee_reports.php
//   │   ├── compliance.php
//   │   └── payroll_prep.php
//   └── partials/
//       ├── agency_header.php
//       ├── navigation.php
//       └── footer.php
```

---

## 🔐 Security Requirements

### CodeIgniter 4 Security Implementation

#### Agency-Specific Access Control
```php
// app/Filters/AgencyAuthFilter.php - Agency-specific authentication
class AgencyAuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is logged in and has agency assignment
        // Filter all data access by user's agency_id
        // Prevent cross-agency data access
        // Validate user permissions within agency context
    }
}
```

#### Employee Data Protection
```php
// app/Libraries/AgencyAuth.php - Extended authentication for agency
class AgencyAuth extends AdminAuth
{
    public function getAgencyEmployees($agency_id)
    {
        // Return only employees belonging to specific agency
        // Filter all queries by agency_id
        // Implement data segregation at query level
    }
    
    public function hasEmployeeAccess($employee_id, $user_agency_id)
    {
        // Verify employee belongs to user's agency
        // Prevent unauthorized employee data access
    }
}
```

#### Document Security
- **File Upload Security**: Restrict file types to documents only
- **Document Access Control**: Agency-specific document access
- **Secure Storage**: Files stored outside web root with access controls
- **Audit Trail**: Track all document access and modifications
- **Encryption**: Sensitive documents encrypted at rest

---

## 📱 Technical Requirements

### Framework & Architecture
- **Backend**: CodeIgniter 4 (PHP 8.1+) with agency-specific controllers
- **Database**: MySQL with proper indexing and foreign key constraints
- **Frontend**: HTML5, CSS3, JavaScript with Bootstrap 5
- **Authentication**: Extended AdminAuth system with agency filtering
- **File Management**: CI4 File Upload with secure document storage
- **Email Integration**: CI4 Email for employee notifications

### Performance Requirements
- **Dashboard Load**: < 3 seconds with employee statistics
- **Employee Search**: < 1 second for name/ID searches
- **Document Upload**: Support files up to 10MB
- **Concurrent Users**: Support 20+ users per agency
- **Database Optimization**: Indexed queries for agency-specific data

### Agency-Specific Features
- **Multi-tenancy**: Complete data segregation by agency_id
- **Customizable Branding**: Agency logo and colors
- **Scalable Architecture**: Support for multiple agencies
- **Backup & Recovery**: Agency-specific data backup capabilities

### Employee Self-Service Features
- **Public Link Security**: Token-based authentication without user accounts
- **Link Expiration**: Automatic expiration after profile submission
- **Dual Upload System**: Support for both agency and employee uploads
- **Mobile Optimization**: Responsive design for employee mobile access
- **Document Tracking**: Unified tracking regardless of upload source
- **Email Notifications**: Automated notifications for link generation and expiration

---

## 🗃️ Implementation Roadmap

### Phase 1: Foundation & Database (Week 1-2)
1. **Employee Database Schema**
   - Create employees table with comprehensive bio-data fields
   - Implement employee_qualifications, employee_documents, employee_banking tables
   - Create employee_profile_links table for self-service functionality
   - Set up proper foreign key relationships and indexes
   - Create data seeders for testing

2. **Agency Authentication Extension**
   - Extend AdminAuth to include agency-specific filtering
   - Create AgencyAuthFilter for data segregation
   - Update routing to handle agency-specific access
   - Test shared login with agency routing

### Phase 2: Employee Self-Service System (Week 3-4)
1. **Public Profile Link Generation**
   - Implement EmployeeProfileLinkModel with token generation
   - Create secure link generation system
   - Build email notification system for link distribution
   - Implement link validation and security checks

2. **Employee Self-Service Interface**
   - Create public-facing employee profile update forms
   - Implement document upload functionality for employees
   - Build mobile-responsive interface for employee access
   - Create link expiration and submission tracking

3. **Dual Upload System**
   - Integrate employee uploads with existing document management
   - Implement upload source tracking (agency vs employee)
   - Create unified document verification workflow
   - Build notification system for new employee uploads

### Phase