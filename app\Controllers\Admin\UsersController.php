<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\AdminBaseController;
use App\Models\AdminUserModel;
use App\Models\AgencyModel;

class UsersController extends AdminBaseController
{
    protected $adminUserModel;
    protected $agencyModel;

    public function __construct()
    {
        $this->adminUserModel = new AdminUserModel();
        $this->agencyModel = new AgencyModel();
    }

    /**
     * Display list of admin users (GET)
     */
    public function index()
    {
        $this->setPageTitle('User Management');
        $this->setSidebarActive('users');
        $this->setBreadcrumbs([
            ['title' => 'User Management', 'url' => '/admin/users', 'active' => true]
        ]);

        $pageScripts = '
        <script>
        $(document).ready(function() {
            // Initialize DataTable
            $("#usersTable").DataTable({
                responsive: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                order: [[6, "desc"]], // Sort by Created date (column index 6) descending
                columnDefs: [
                    {
                        targets: [7], // Actions column
                        orderable: false,
                        searchable: false
                    },
                    {
                        targets: [0, 1, 2, 3, 4, 5], // User, Contact, Role, Agency Type, Agency Name, Status columns
                        className: "align-middle"
                    }
                ],
                language: {
                    search: "Search users:",
                    lengthMenu: "Show _MENU_ users per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ users",
                    infoEmpty: "No users found",
                    infoFiltered: "(filtered from _MAX_ total users)",
                    emptyTable: "No users available",
                    zeroRecords: "No matching users found"
                },
                // Bootstrap-friendly DOM structure with proper search box
                dom: \'<"row mb-3"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>\' +
                     \'<"row"<"col-sm-12"tr>>\' +
                     \'<"row mt-3"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>\',
                drawCallback: function() {
                    // Re-initialize tooltips after table redraw
                    $("[title]").tooltip();
                }
            });
        });
        </script>';

        $data = [
            'adminUsers' => $this->adminUserModel->getAdminUsersForDataTable(),
            'currentUser' => $this->getCurrentUser(),
            'pageScripts' => $pageScripts,
        ];

        return $this->render('admin/users/admin_users_index', $data);
    }

    /**
     * Display create form (GET)
     */
    public function create()
    {
        $this->setPageTitle('Create User');
        $this->setSidebarActive('users');
        $this->setBreadcrumbs([
            ['title' => 'User Management', 'url' => '/admin/users', 'active' => false],
            ['title' => 'Create New', 'url' => '/admin/users/create', 'active' => true]
        ]);

        $data = [
            'roleOptions' => $this->adminUserModel->getRoleOptions(),
            'statusOptions' => $this->adminUserModel->getStatusOptions(),
            'agencyOptions' => $this->getAgencyOptions(),
            'validation' => \Config\Services::validation(),
        ];

        return $this->render('admin/users/admin_users_create', $data);
    }

    /**
     * Process create form (POST)
     */
    public function store()
    {
        // Validation rules
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[admin_users.username]',
            'email' => 'required|valid_email|max_length[100]|is_unique[admin_users.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]',
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'phone_number' => 'permit_empty|max_length[20]',
            'role' => 'required|in_list[supervisor,user,admin]',
            'agency_id' => 'permit_empty|integer',
            'is_active' => 'permit_empty|in_list[0,1]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // Prepare data for insertion
        $data = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'password_hash' => $this->request->getPost('password'), // Will be hashed by model
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'phone_number' => $this->request->getPost('phone_number'),
            'role' => $this->request->getPost('role'),
            'agency_id' => $this->request->getPost('agency_id') ?: null,
            'is_active' => $this->request->getPost('is_active') ?? 1,
            'created_by' => $this->getCurrentUserId()
        ];

        if ($this->adminUserModel->insert($data)) {
            $this->setFlashMessage('success', 'User created successfully.');
            return redirect()->to('/admin/users');
        }

        $this->setFlashMessage('error', 'Failed to create user. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Display single admin user (GET)
     */
    public function show($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);
        
        if (!$adminUser) {
            $this->setFlashMessage('error', 'User not found.');
            return redirect()->to('/admin/users');
        }

        // Get agency information if user is assigned to one
        $agency = null;
        if ($adminUser['agency_id']) {
            $agency = $this->agencyModel->getAgency($adminUser['agency_id']);
        }

        $this->setPageTitle('View User');
        $this->setSidebarActive('users');
        $this->setBreadcrumbs([
            ['title' => 'User Management', 'url' => '/admin/users', 'active' => false],
            ['title' => $adminUser['first_name'] . ' ' . $adminUser['last_name'], 'url' => '/admin/users/' . $id, 'active' => true]
        ]);

        $data = [
            'adminUser' => $adminUser,
            'agency' => $agency,
            'roleOptions' => $this->adminUserModel->getRoleOptions(),
            'currentUser' => $this->getCurrentUser(),
        ];

        return $this->render('admin/users/admin_users_show', $data);
    }

    /**
     * Display edit form (GET)
     */
    public function edit($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);

        if (!$adminUser) {
            $this->setFlashMessage('error', 'User not found.');
            return redirect()->to('/admin/users');
        }

        // Prevent editing admin users
        if ($adminUser['role'] === 'admin') {
            $this->setFlashMessage('error', 'Admin users cannot be edited.');
            return redirect()->to('/admin/users/' . $id);
        }

        $this->setPageTitle('Edit User');
        $this->setSidebarActive('users');
        $this->setBreadcrumbs([
            ['title' => 'User Management', 'url' => '/admin/users', 'active' => false],
            ['title' => $adminUser['first_name'] . ' ' . $adminUser['last_name'], 'url' => '/admin/users/' . $id, 'active' => false],
            ['title' => 'Edit', 'url' => '/admin/users/' . $id . '/edit', 'active' => true]
        ]);

        $data = [
            'adminUser' => $adminUser,
            'roleOptions' => $this->adminUserModel->getRoleOptions(),
            'statusOptions' => $this->adminUserModel->getStatusOptions(),
            'agencyOptions' => $this->getAgencyOptions(),
            'validation' => \Config\Services::validation(),
        ];

        return $this->render('admin/users/admin_users_edit', $data);
    }

    /**
     * Process edit form (POST)
     */
    public function update($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);

        if (!$adminUser) {
            $this->setFlashMessage('error', 'User not found.');
            return redirect()->to('/admin/users');
        }

        // Prevent updating admin users
        if ($adminUser['role'] === 'admin') {
            $this->setFlashMessage('error', 'Admin users cannot be updated.');
            return redirect()->to('/admin/users/' . $id);
        }

        // Basic validation rules (without unique checks)
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]',
            'email' => 'required|valid_email|max_length[100]',
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'phone_number' => 'permit_empty|max_length[20]',
            'role' => 'required|in_list[supervisor,user,admin]',
            'agency_id' => 'permit_empty|integer',
            'is_active' => 'permit_empty|in_list[0,1]'
        ];

        // Add password validation only if password is provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[4]';
            $rules['confirm_password'] = 'matches[password]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // Separate unique validation - only check records NOT equal to current user ID
        $username = $this->request->getPost('username');
        $email = $this->request->getPost('email');

        // Check username uniqueness (exclude current user)
        $existingUsername = $this->adminUserModel->where('username', $username)->where('id !=', $id)->first();
        if ($existingUsername) {
            $this->setFlashMessage('error', 'Username already exists.');
            return redirect()->back()->withInput();
        }

        // Check email uniqueness (exclude current user)
        $existingEmail = $this->adminUserModel->where('email', $email)->where('id !=', $id)->first();
        if ($existingEmail) {
            $this->setFlashMessage('error', 'Email already exists.');
            return redirect()->back()->withInput();
        }

        // Prepare data for update
        $data = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'phone_number' => $this->request->getPost('phone_number'),
            'role' => $this->request->getPost('role'),
            'agency_id' => $this->request->getPost('agency_id') ?: null,
            'is_active' => $this->request->getPost('is_active') ?? 1,
        ];

        // Add password if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password_hash'] = $this->request->getPost('password'); // Will be hashed by model
        }

        // Disable model validation since we did our own validation above
        $this->adminUserModel->skipValidation(true);
        if ($this->adminUserModel->update($id, $data)) {
            $this->setFlashMessage('success', 'User updated successfully.');
            return redirect()->to('/admin/users/' . $id);
        }

        $this->setFlashMessage('error', 'Failed to update user. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Delete user (POST)
     */
    public function delete($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);

        if (!$adminUser) {
            $this->setFlashMessage('error', 'User not found.');
            return redirect()->to('/admin/users');
        }

        // Prevent deletion of current user
        if ($id == $this->getCurrentUserId()) {
            $this->setFlashMessage('error', 'You cannot delete your own account.');
            return redirect()->to('/admin/users');
        }

        // Prevent deletion of admin users
        if ($adminUser['role'] === 'admin') {
            $this->setFlashMessage('error', 'Admin users cannot be deleted.');
            return redirect()->to('/admin/users');
        }

        if ($this->adminUserModel->delete($id)) {
            $this->setFlashMessage('success', 'User deleted successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to delete user. Please try again.');
        }

        return redirect()->to('/admin/users');
    }

    /**
     * Get agency options for dropdown
     */
    private function getAgencyOptions(): array
    {
        $agencies = $this->agencyModel->where('status', 'active')->findAll();
        $options = ['' => '-- Select Agency (Optional) --'];
        
        foreach ($agencies as $agency) {
            $options[$agency['id']] = $agency['agency_code'] . ' - ' . $agency['name'];
        }
        
        return $options;
    }
}
