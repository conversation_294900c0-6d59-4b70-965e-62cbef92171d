<?php

namespace App\Models;

use CodeIgniter\Model;

class AgencyModel extends Model
{
    protected $table = 'agencies';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'agency_code',
        'name',
        'address',
        'phone',
        'email',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'agency_code' => 'required|min_length[2]|max_length[20]|is_unique[agencies.agency_code,id,{id}]',
        'name' => 'required|min_length[3]|max_length[255]',
        'address' => 'permit_empty|max_length[1000]',
        'phone' => 'permit_empty|max_length[20]',
        'email' => 'permit_empty|valid_email|max_length[100]',
        'status' => 'required|in_list[active,inactive,suspended]'
    ];

    protected $validationMessages = [
        'agency_code' => [
            'required' => 'Agency code is required',
            'min_length' => 'Agency code must be at least 2 characters long',
            'max_length' => 'Agency code cannot exceed 20 characters',
            'is_unique' => 'Agency code already exists'
        ],
        'name' => [
            'required' => 'Agency name is required',
            'min_length' => 'Agency name must be at least 3 characters long',
            'max_length' => 'Agency name cannot exceed 255 characters'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address',
            'max_length' => 'Email cannot exceed 100 characters'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, inactive, or suspended'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setCreatedBy'];
    protected $beforeUpdate = ['setUpdatedBy'];
    protected $beforeDelete = ['setDeletedBy'];

    /**
     * Set created_by before insert
     */
    protected function setCreatedBy(array $data)
    {
        if (!isset($data['data']['created_by'])) {
            $data['data']['created_by'] = $this->getCurrentUserId();
        }
        return $data;
    }

    /**
     * Set updated_by before update
     */
    protected function setUpdatedBy(array $data)
    {
        $data['data']['updated_by'] = $this->getCurrentUserId();
        return $data;
    }

    /**
     * Set deleted_by before delete
     */
    protected function setDeletedBy(array $data)
    {
        $data['data']['deleted_by'] = $this->getCurrentUserId();
        return $data;
    }

    /**
     * Get current user ID from session
     */
    private function getCurrentUserId(): ?int
    {
        $session = session();
        return $session->get('dakoii_user_id');
    }

    /**
     * Get all agencies with pagination
     */
    public function getAgencies(int $perPage = 10, string $search = '')
    {
        if (!empty($search)) {
            $this->groupStart()
                 ->like('agency_code', $search)
                 ->orLike('name', $search)
                 ->orLike('email', $search)
                 ->groupEnd();
        }
        
        return $this->orderBy('created_at', 'DESC')->paginate($perPage);
    }

    /**
     * Get agency by ID
     */
    public function getAgency(int $id): ?array
    {
        return $this->find($id);
    }

    /**
     * Get active agencies count
     */
    public function getActiveAgenciesCount(): int
    {
        return $this->where('status', 'active')->countAllResults();
    }

    /**
     * Get agencies by status
     */
    public function getAgenciesByStatus(string $status): array
    {
        return $this->where('status', $status)->findAll();
    }

    /**
     * Get status options for forms
     */
    public function getStatusOptions(): array
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'suspended' => 'Suspended'
        ];
    }

    /**
     * Generate unique agency code
     */
    public function generateAgencyCode(string $name): string
    {
        // Create base code from name (first 3 letters + random number)
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $name), 0, 3));
        $counter = 1;
        
        do {
            $code = $baseCode . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $exists = $this->where('agency_code', $code)->first();
            $counter++;
        } while ($exists && $counter <= 999);
        
        return $code;
    }

    /**
     * Get agency statistics
     */
    public function getAgencyStats(): array
    {
        return [
            'total' => $this->countAllResults(false),
            'active' => $this->where('status', 'active')->countAllResults(false),
            'inactive' => $this->where('status', 'inactive')->countAllResults(false),
            'suspended' => $this->where('status', 'suspended')->countAllResults(false)
        ];
    }

    /**
     * Check if agency code exists (excluding current agency)
     */
    public function isAgencyCodeUnique(string $code, ?int $excludeId = null): bool
    {
        $query = $this->where('agency_code', $code);
        
        if ($excludeId) {
            $query->where('id !=', $excludeId);
        }
        
        return $query->first() === null;
    }
}
