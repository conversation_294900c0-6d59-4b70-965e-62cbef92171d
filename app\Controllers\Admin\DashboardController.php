<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\AdminBaseController;
use App\Models\AdminUserModel;
use App\Models\AgencyModel;

class DashboardController extends AdminBaseController
{
    protected $adminUserModel;
    protected $agencyModel;

    public function __construct()
    {
        $this->adminUserModel = new AdminUserModel();
        $this->agencyModel = new AgencyModel();
    }

    /**
     * Display dashboard (GET)
     */
    public function index()
    {
        $this->setPageTitle('Dashboard');
        $this->setSidebarActive('dashboard');
        $this->setBreadcrumbs([
            ['title' => 'Dashboard', 'url' => '/admin/dashboard', 'active' => true]
        ]);

        // Get dashboard statistics
        $data = [
            'stats' => $this->getDashboardStats(),
            'recentUsers' => $this->getRecentUsers(),
            'usersByRole' => $this->getUsersByRole(),
            'agencyStats' => $this->getAgencyStats(),
            'currentUser' => $this->currentUser
        ];

        return $this->render('admin/dashboard/index', $data);
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        $totalUsers = $this->adminUserModel->countAllResults(false);
        $activeUsers = $this->adminUserModel->where('is_active', 1)->countAllResults(false);
        $inactiveUsers = $totalUsers - $activeUsers;
        
        $totalAgencies = $this->agencyModel->countAllResults(false);
        $activeAgencies = $this->agencyModel->where('status', 'active')->countAllResults(false);

        // Admin portal users (agency_id is null)
        $adminPortalUsers = $this->adminUserModel->where('agency_id IS NULL')->countAllResults(false);
        
        // Agency portal users (agency_id is not null)
        $agencyPortalUsers = $this->adminUserModel->where('agency_id IS NOT NULL')->countAllResults(false);

        return [
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'inactive_users' => $inactiveUsers,
            'total_agencies' => $totalAgencies,
            'active_agencies' => $activeAgencies,
            'admin_portal_users' => $adminPortalUsers,
            'agency_portal_users' => $agencyPortalUsers,
            'user_growth_percentage' => $this->calculateUserGrowth(),
            'agency_growth_percentage' => $this->calculateAgencyGrowth()
        ];
    }

    /**
     * Get recent users (last 10)
     */
    private function getRecentUsers(): array
    {
        return $this->adminUserModel
                    ->select('id, first_name, last_name, email, role, is_active, created_at')
                    ->orderBy('created_at', 'DESC')
                    ->limit(10)
                    ->findAll();
    }

    /**
     * Get users grouped by role
     */
    private function getUsersByRole(): array
    {
        $roles = ['admin', 'supervisor', 'user'];
        $usersByRole = [];

        foreach ($roles as $role) {
            $count = $this->adminUserModel
                          ->where('role', $role)
                          ->where('is_active', 1)
                          ->countAllResults(false);
            $usersByRole[$role] = $count;
        }

        return $usersByRole;
    }

    /**
     * Get agency statistics
     */
    private function getAgencyStats(): array
    {
        $agencies = $this->agencyModel->findAll();
        $agencyStats = [];

        foreach ($agencies as $agency) {
            $userCount = $this->adminUserModel
                              ->where('agency_id', $agency['id'])
                              ->where('is_active', 1)
                              ->countAllResults(false);
            
            $agencyStats[] = [
                'id' => $agency['id'],
                'name' => $agency['name'],
                'agency_code' => $agency['agency_code'],
                'status' => $agency['status'],
                'user_count' => $userCount
            ];
        }

        // Sort by user count descending
        usort($agencyStats, function($a, $b) {
            return $b['user_count'] - $a['user_count'];
        });

        return array_slice($agencyStats, 0, 10); // Top 10 agencies
    }

    /**
     * Calculate user growth percentage (mock calculation)
     */
    private function calculateUserGrowth(): float
    {
        // This is a simplified calculation
        // In a real application, you would compare with previous period
        $currentMonth = $this->adminUserModel
                             ->where('MONTH(created_at)', date('m'))
                             ->where('YEAR(created_at)', date('Y'))
                             ->countAllResults(false);
        
        $previousMonth = $this->adminUserModel
                              ->where('MONTH(created_at)', date('m', strtotime('-1 month')))
                              ->where('YEAR(created_at)', date('Y', strtotime('-1 month')))
                              ->countAllResults(false);

        if ($previousMonth == 0) {
            return $currentMonth > 0 ? 100.0 : 0.0;
        }

        return round((($currentMonth - $previousMonth) / $previousMonth) * 100, 1);
    }

    /**
     * Calculate agency growth percentage (mock calculation)
     */
    private function calculateAgencyGrowth(): float
    {
        // This is a simplified calculation
        $currentMonth = $this->agencyModel
                             ->where('MONTH(created_at)', date('m'))
                             ->where('YEAR(created_at)', date('Y'))
                             ->countAllResults(false);
        
        $previousMonth = $this->agencyModel
                              ->where('MONTH(created_at)', date('m', strtotime('-1 month')))
                              ->where('YEAR(created_at)', date('Y', strtotime('-1 month')))
                              ->countAllResults(false);

        if ($previousMonth == 0) {
            return $currentMonth > 0 ? 100.0 : 0.0;
        }

        return round((($currentMonth - $previousMonth) / $previousMonth) * 100, 1);
    }

    /**
     * Get quick actions based on user role
     */
    private function getQuickActions(): array
    {
        $actions = [];

        if ($this->hasPermission('users.create')) {
            $actions[] = [
                'title' => 'Add New User',
                'url' => '/admin/users/create',
                'icon' => 'person_add',
                'color' => 'primary'
            ];
        }

        if ($this->hasPermission('agency_users.assign')) {
            $actions[] = [
                'title' => 'Assign Users to Agency',
                'url' => '/admin/agency-users/assign',
                'icon' => 'business',
                'color' => 'success'
            ];
        }

        if ($this->hasPermission('reports.view')) {
            $actions[] = [
                'title' => 'View Reports',
                'url' => '/admin/reports',
                'icon' => 'assessment',
                'color' => 'info'
            ];
        }

        $actions[] = [
            'title' => 'User Management',
            'url' => '/admin/users',
            'icon' => 'people',
            'color' => 'secondary'
        ];

        return $actions;
    }

    /**
     * Get system alerts/notifications
     */
    private function getSystemAlerts(): array
    {
        $alerts = [];

        // Check for inactive users
        $inactiveUsers = $this->adminUserModel->where('is_active', 0)->countAllResults(false);
        if ($inactiveUsers > 0) {
            $alerts[] = [
                'type' => 'warning',
                'icon' => 'warning',
                'title' => 'Inactive Users',
                'message' => "You have {$inactiveUsers} inactive user(s) that may need attention.",
                'action_url' => '/admin/users?status=inactive',
                'action_text' => 'View Inactive Users'
            ];
        }

        // Check for agencies without users
        $agenciesWithoutUsers = 0;
        $agencies = $this->agencyModel->where('status', 'active')->findAll();
        foreach ($agencies as $agency) {
            $userCount = $this->adminUserModel
                              ->where('agency_id', $agency['id'])
                              ->countAllResults(false);
            if ($userCount == 0) {
                $agenciesWithoutUsers++;
            }
        }

        if ($agenciesWithoutUsers > 0) {
            $alerts[] = [
                'type' => 'info',
                'icon' => 'info',
                'title' => 'Agencies Without Users',
                'message' => "{$agenciesWithoutUsers} active agencies have no assigned users.",
                'action_url' => '/admin/agency-users',
                'action_text' => 'Manage Agency Users'
            ];
        }

        return $alerts;
    }
}
