<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($pageTitle ?? 'Admin Portal') ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Material Design Bootstrap -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom Admin Theme CSS -->
    <link href="<?= base_url('assets/css/admin-light-theme.css') ?>" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= csrf_token() ?>">
    <meta name="csrf-hash" content="<?= csrf_hash() ?>">
    
    <style>
        :root {
            --primary-blue: #1A4E8C;
            --engine-red: #C8102E;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --light-gray: #F8F9FA;
            --border-gray: #DEE2E6;
            --gradient-primary: linear-gradient(135deg, #1A4E8C 0%, #2563EB 100%);
            --gradient-secondary: linear-gradient(135deg, #C8102E 0%, #EF4444 100%);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--light-gray);
            color: var(--text-dark);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .sidebar {
            width: 280px;
            min-height: 100vh;
            background: var(--text-light);
            border-right: 1px solid var(--border-gray);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background-color: var(--light-gray);
            width: calc(100% - 280px);
            flex: 1;
        }

        .top-navbar {
            background: var(--text-light);
            border-bottom: 1px solid var(--border-gray);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            width: 100%;
        }

        .sidebar-brand {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid var(--border-gray);
        }

        .sidebar-brand img {
            width: 60px;
            height: 60px;
            margin-bottom: 0.5rem;
        }

        .sidebar-brand h5 {
            color: var(--primary-blue);
            font-family: 'Roboto Condensed', sans-serif;
            font-weight: 700;
            margin: 0;
        }

        .sidebar-brand small {
            color: #6c757d;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            color: var(--text-dark);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background-color: var(--light-gray);
            color: var(--primary-blue);
        }

        .nav-link.active {
            background: var(--gradient-primary);
            color: var(--text-light);
        }

        .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .content-wrapper {
            padding: 2rem;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .page-header {
            background: var(--text-light);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .page-title {
            font-family: 'Roboto Condensed', sans-serif;
            font-weight: 700;
            color: var(--primary-blue);
            margin: 0;
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0.5rem 0 0 0;
        }

        .breadcrumb-item a {
            color: var(--primary-blue);
            text-decoration: none;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            background: var(--gradient-primary);
            color: var(--text-light);
            border-radius: 10px 10px 0 0 !important;
            padding: 1rem 1.5rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
        }

        .btn-danger {
            background: var(--gradient-secondary);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid var(--border-gray);
            padding: 0.75rem;
        }

        .form-control:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
        }

        .user-profile {
            padding: 1rem;
            border-top: 1px solid var(--border-gray);
            margin-top: auto;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            font-weight: 600;
        }

        /* Ensure full width utilization */
        .container-fluid {
            padding-left: 0;
            padding-right: 0;
            max-width: 100%;
        }

        .row {
            margin-left: 0;
            margin-right: 0;
        }

        .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
        .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
        .col-auto, .col-sm, .col-sm-1, .col-sm-2, .col-sm-3,
        .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8,
        .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-auto,
        .col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4,
        .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9,
        .col-md-10, .col-md-11, .col-md-12, .col-md-auto,
        .col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4,
        .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9,
        .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-auto,
        .col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4,
        .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9,
        .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-auto {
            padding-left: 15px;
            padding-right: 15px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .content-wrapper {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Brand -->
            <div class="sidebar-brand">
                <img src="<?= base_url('assets/images/chs-logo.png') ?>" alt="CHS Logo">
                <h5>Admin Portal</h5>
                <small>Christian Health Services PNG</small>
            </div>
            
            <!-- Navigation -->
            <nav class="sidebar-nav">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?= ($sidebarActive === 'dashboard') ? 'active' : '' ?>" href="<?= base_url('/admin/dashboard') ?>">
                            <i class="material-icons">dashboard</i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($sidebarActive === 'users') ? 'active' : '' ?>" href="<?= base_url('/admin/users') ?>">
                            <i class="material-icons">people</i>
                            User Management
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?= ($sidebarActive === 'reports') ? 'active' : '' ?>" href="<?= base_url('/admin/reports') ?>">
                            <i class="material-icons">assessment</i>
                            Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= ($sidebarActive === 'settings') ? 'active' : '' ?>" href="<?= base_url('/admin/settings') ?>">
                            <i class="material-icons">settings</i>
                            Settings
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- User Profile -->
            <?php if (isset($currentUser) && $currentUser): ?>
            <div class="user-profile">
                <div class="d-flex align-items-center mb-3">
                    <div class="user-avatar">
                        <?= strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1)) ?>
                    </div>
                    <div class="ms-3 flex-grow-1">
                        <div class="fw-bold"><?= esc($currentUser['first_name'] . ' ' . $currentUser['last_name']) ?></div>
                        <small class="text-muted"><?= ucfirst(esc($currentUser['role'])) ?></small>
                    </div>
                </div>

                <!-- Profile and Logout Menu Items -->
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?= ($sidebarActive === 'profile') ? 'active' : '' ?>" href="<?= base_url('/admin/profile') ?>">
                            <i class="material-icons">person</i>
                            Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <form action="<?= base_url('/admin/logout') ?>" method="post" class="d-inline w-100">
                            <?= csrf_field() ?>
                            <button type="submit" class="nav-link text-start w-100 border-0 bg-transparent">
                                <i class="material-icons">logout</i>
                                Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
            <?php endif; ?>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navbar -->
            <div class="top-navbar d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary d-md-none me-3" type="button" onclick="toggleSidebar()">
                        <i class="material-icons">menu</i>
                    </button>
                    <h6 class="mb-0 text-muted">CHealth Wokman - Workforce Management System</h6>
                </div>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3"><?= date('F j, Y') ?></span>
                    <div class="text-primary">
                        <i class="material-icons">access_time</i>
                        <span id="current-time"><?= date('H:i:s') ?></span>
                    </div>
                </div>
            </div>

            <!-- Content Wrapper -->
            <div class="content-wrapper">
                <!-- Page Header -->
                <?php if (isset($pageTitle) || isset($breadcrumbs)): ?>
                <div class="page-header">
                    <?php if (isset($pageTitle)): ?>
                    <h1 class="page-title"><?= esc(str_replace(' - Admin Portal', '', $pageTitle)) ?></h1>
                    <?php endif; ?>
                    
                    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <?php foreach ($breadcrumbs as $crumb): ?>
                                <?php if (isset($crumb['active']) && $crumb['active']): ?>
                                    <li class="breadcrumb-item active" aria-current="page"><?= esc($crumb['title']) ?></li>
                                <?php else: ?>
                                    <li class="breadcrumb-item"><a href="<?= esc($crumb['url']) ?>"><?= esc($crumb['title']) ?></a></li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </nav>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="material-icons me-2">check_circle</i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="material-icons me-2">error</i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('warning')): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="material-icons me-2">warning</i>
                    <?= session()->getFlashdata('warning') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('info')): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="material-icons me-2">info</i>
                    <?= session()->getFlashdata('info') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Main Content -->
                <?= $content ?? '' ?>
            </div>
        </div>
    </div>

    <!-- jQuery (load first - required for DataTables and other libraries) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Material Design Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <script>
        // Update time every second
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { hour12: false });
            document.getElementById('current-time').textContent = timeString;
        }
        
        setInterval(updateTime, 1000);

        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = event.target.closest('[onclick="toggleSidebar()"]');
            
            if (!sidebar.contains(event.target) && !toggleBtn && window.innerWidth <= 768) {
                sidebar.classList.remove('show');
            }
        });

        // CSRF token setup for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfHash = document.querySelector('meta[name="csrf-hash"]').getAttribute('content');
    </script>

    <!-- Page-specific scripts -->
    <?php if (isset($pageScripts)): ?>
        <?= $pageScripts ?>
    <?php endif; ?>
</body>
</html>
