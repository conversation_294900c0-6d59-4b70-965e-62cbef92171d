# CHS PNG 4-Portal System Design Specification

## System Overview
A comprehensive employee management system for Christian Health Services PNG with four distinct portals serving different user roles and organizational levels.

## Portal Architecture

### 1. **DAKOII PORTAL** (Super Administrator)
*System-wide administration and foundational setup*

#### User Access
- Super Admin login credentials
- Highest privilege level across all portals

#### Core Functions
- **Admin Management (CRUD)**
  - Create/Read/Update/Delete admin users
  - Assign admin roles and permissions
  - Manage admin access levels
  - Admin user authentication management

- **Agency Management (CRUD)**
  - Create new health facilities/agencies
  - Configure agency details (location, contact info, facility type)
  - Update agency information and status
  - Deactivate/reactivate agencies
  - Assign agencies to regional supervisors

#### Additional Features
- System-wide reporting and analytics
- Audit logs for all administrative actions
- Backup and data management
- System configuration settings
- License and subscription management

---

### 2. **ORGANIZATION MANAGEMENT PORTAL** 
*Central HR administration for CHS PNG*

#### User Access
- Organization administrators
- HR managers
- Regional supervisors

#### Core Functions
- **Organization User Management (CRUD)**
  - Create supervisor accounts
  - Manage regular organization users
  - Define reporting hierarchies
  - Link users to specific agencies (for agency-based users)
  - Set user permissions and access levels

- **Reporting Structure Management**
  - Define "reports to" relationships
  - Create organizational charts
  - Manage approval workflows
  - Assign agency oversight responsibilities

#### Document Management
- Manage organization-wide forms and templates
- Handle payroll processing coordination
- Oversee compliance across all agencies
- Generate consolidated reports

#### Additional Features
- Cross-agency reporting and analytics
- Policy distribution and updates
- Training program coordination
- Performance review oversight

---

### 3. **AGENCY PORTAL**
*Individual health facility management*

#### User Access & Routing
- Agency-linked users automatically redirect here upon login
- Facility managers (OIC - Officer in Charge)
- Agency administrators
- Department heads

#### Employee Registration & Management
- **New Employee Onboarding**
  - Bio-data form completion (as per CHS Bio-Data Form 1)
  - Document upload and verification checklist
  - Employment contract management
  - Pre-vetting checklist processing

- **Employee Records Management**
  - Individual employee profiles
  - Qualification tracking (basic + additional qualifications)
  - Professional membership and license management
  - Employment history records
  - Medical certificate tracking
  - Police clearance status

#### Document Processing
- **Required Documents Checklist**
  - Individual bio-data form with photo
  - Signed employment contract
  - Updated CV
  - Educational credentials (certificates/degrees)
  - Professional licenses and memberships
  - NASFUND documentation
  - NID/Employment ID verification
  - Medical certificates
  - Police clearance
  - IRC salary & wages declaration

#### Payroll Coordination
- **Banking & Financial Details**
  - Primary account information
  - NASFUND member details
  - Salary processing preparation
  - Tax declaration management

#### Additional Features
- Agency-specific reporting
- Staff scheduling (if applicable)
- Performance evaluations
- Leave management
- Training records

---

### 4. **EMPLOYEE PORTAL**
*Individual employee self-service*

#### User Access
- Login using employee file number
- Secure personal authentication
- Role-based access to personal information only

#### Personal Information Management
- **Profile Management**
  - View/update personal details
  - Contact information updates
  - Emergency contact management
  - Family information (spouse, children)

- **Document Management**
  - Upload required documents
  - View document status and approval
  - Download forms and certificates
  - Track application progress

#### Professional Information
- **Qualification Management**
  - Update additional qualifications
  - Upload new certificates
  - Professional membership renewals
  - License status tracking

- **Employment Records**
  - View employment history
  - Access pay slips (when integrated)
  - Leave balance and requests
  - Performance review history

#### Self-Service Functions
- Password management
- Notification preferences
- Download personal reports
- Submit update requests to supervisors

---

## System Integration Features

### Cross-Portal Data Flow
1. **Dakoii → Organization**: Agency and admin user creation
2. **Organization → Agency**: User assignments and policy distribution
3. **Agency → Employee**: Individual record access and updates
4. **Employee → Agency**: Document submissions and update requests

### Reporting Hierarchy
- **System Level**: Dakoii portal comprehensive overview
- **Organization Level**: Cross-agency analytics and compliance
- **Agency Level**: Facility-specific metrics and staff management
- **Individual Level**: Personal records and self-service options

### Security & Access Control
- Role-based authentication
- Data segregation by organizational level
- Audit trails across all portals
- Secure document handling
- GDPR/privacy compliance for personal data

### Technical Considerations
- Responsive design for mobile and desktop access
- Offline capability for remote health facilities
- Integration with NASFUND and IRC systems
- Document scanning and OCR capabilities
- Automated workflow approvals
- Email/SMS notifications

## Implementation Priority
1. **Phase 1**: Dakoii and Organization portals (foundation)
2. **Phase 2**: Agency portal (operational core)
3. **Phase 3**: Employee portal (self-service)
4. **Phase 4**: Advanced integrations and analytics