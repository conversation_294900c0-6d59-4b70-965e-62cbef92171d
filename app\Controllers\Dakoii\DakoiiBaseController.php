<?php

namespace App\Controllers\Dakoii;

use App\Controllers\BaseController;
use App\Libraries\DakoiiAuth;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class DakoiiBaseController extends BaseController
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend DakoiiBaseController.
     *
     * @var array
     */
    protected $helpers = ['form', 'url', 'html'];

    /**
     * DakoiiAuth instance
     *
     * @var DakoiiAuth
     */
    protected $auth;

    /**
     * Session instance
     *
     * @var \CodeIgniter\Session\Session
     */
    protected $session;

    /**
     * Current user data
     *
     * @var array|null
     */
    protected $currentUser;

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.
        $this->auth = new DakoiiAuth();
        $this->session = \Config\Services::session();
        $this->currentUser = $this->auth->user();

        // Set common view data
        $this->setCommonViewData();
    }

    /**
     * Set common data for all views
     */
    protected function setCommonViewData()
    {
        $data = [
            'currentUser' => $this->currentUser,
            'pageTitle' => 'Dakoii Portal',
            'breadcrumbs' => [],
            'sidebarActive' => '',
        ];

        // Make data available to all views
        $this->data = $data;
    }

    /**
     * Render view with template
     */
    protected function render(string $view, array $data = [], string $template = 'templates/dakoii_portal_template')
    {
        // Merge with common data
        $data = array_merge($this->data, $data);

        // Set content for template
        $data['content'] = view($view, $data);

        return view($template, $data);
    }

    /**
     * Set page title
     */
    protected function setPageTitle(string $title)
    {
        $this->data['pageTitle'] = $title . ' - Dakoii Portal';
    }

    /**
     * Set breadcrumbs
     */
    protected function setBreadcrumbs(array $breadcrumbs)
    {
        $this->data['breadcrumbs'] = $breadcrumbs;
    }

    /**
     * Set active sidebar item
     */
    protected function setSidebarActive(string $active)
    {
        $this->data['sidebarActive'] = $active;
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return $this->auth->check();
    }

    /**
     * Redirect to login if not authenticated
     */
    protected function requireAuth()
    {
        if (!$this->isAuthenticated()) {
            $this->session->set('dakoii_intended_url', current_url());
            return redirect()->to('/dakoii/login')->with('error', 'Please log in to access this page.');
        }
    }

    /**
     * Get current user ID
     */
    protected function getCurrentUserId(): ?int
    {
        return $this->auth->id();
    }

    /**
     * Flash message helper
     */
    protected function setFlashMessage(string $type, string $message)
    {
        $this->session->setFlashdata($type, $message);
    }
}
