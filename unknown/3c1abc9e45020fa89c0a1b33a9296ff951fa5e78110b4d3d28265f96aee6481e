<?php

namespace App\Controllers\Dakoii;

use App\Controllers\Dakoii\DakoiiBaseController;

class AuthController extends DakoiiBaseController
{
    /**
     * Display login form (GET)
     */
    public function login()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/dakoii/dashboard');
        }

        $this->setPageTitle('Login');
        
        $data = [
            'validation' => \Config\Services::validation(),
        ];

        return view('dakoii/auth/login', $data);
    }

    /**
     * Process login form (POST)
     */
    public function authenticate()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/dakoii/dashboard');
        }

        // Validation rules
        $rules = [
            'identifier' => [
                'label' => 'Username/Email',
                'rules' => 'required|min_length[3]|max_length[100]'
            ],
            'password' => [
                'label' => 'Password',
                'rules' => 'required|min_length[6]'
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        $identifier = $this->request->getPost('identifier');
        $password = $this->request->getPost('password');

        // Attempt login
        if ($this->auth->attempt($identifier, $password)) {
            // Check for intended URL
            $intendedUrl = $this->session->get('dakoii_intended_url');
            $this->session->remove('dakoii_intended_url');

            $redirectUrl = $intendedUrl ?: '/dakoii/dashboard';
            
            $this->setFlashMessage('success', 'Welcome back, ' . $this->auth->fullName() . '!');
            return redirect()->to($redirectUrl);
        }

        // Login failed
        $this->setFlashMessage('error', 'Invalid username/email or password. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Process logout (POST)
     */
    public function logout()
    {
        if ($this->isAuthenticated()) {
            $this->auth->logout();
            $this->setFlashMessage('success', 'You have been logged out successfully.');
        }

        return redirect()->to('/dakoii/login');
    }

    /**
     * Display forgot password form (GET)
     */
    public function forgotPassword()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/dakoii/dashboard');
        }

        $this->setPageTitle('Forgot Password');
        
        return view('dakoii/auth/forgot_password');
    }

    /**
     * Process forgot password form (POST)
     */
    public function sendResetLink()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/dakoii/dashboard');
        }

        // Validation rules
        $rules = [
            'email' => [
                'label' => 'Email',
                'rules' => 'required|valid_email|max_length[100]'
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // For now, just show a success message
        // In a real implementation, you would send an email with reset link
        $this->setFlashMessage('success', 'If an account with that email exists, a password reset link has been sent.');
        return redirect()->to('/dakoii/login');
    }
}
