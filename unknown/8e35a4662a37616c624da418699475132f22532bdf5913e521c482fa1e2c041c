<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Dakoii Portal</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom Dark Theme CSS -->
    <link href="<?= base_url('assets/css/dakoii-dark-theme.css') ?>" rel="stylesheet">
</head>
<body>
    <div class="login-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card">
                        <div class="card-body p-5">
                            <!-- Logo and Title -->
                            <div class="text-center mb-4">
                                <img src="<?= base_url('assets/images/dakoii-logo.png') ?>" alt="Dakoii Logo" class="login-logo mb-3">
                                <h3 class="text-light mb-2">Dakoii Portal</h3>
                                <p class="text-muted">Super Administrator Access</p>
                            </div>
                            
                            <!-- Flash Messages -->
                            <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <?= esc(session()->getFlashdata('error')) ?>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (session()->getFlashdata('success')): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="bi bi-check-circle me-2"></i>
                                <?= esc(session()->getFlashdata('success')) ?>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Login Form -->
                            <form action="<?= base_url('dakoii/authenticate') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <!-- Username/Email Field -->
                                <div class="mb-3">
                                    <label for="identifier" class="form-label">
                                        <i class="bi bi-person me-1"></i>
                                        Username or Email
                                    </label>
                                    <input type="text" 
                                           class="form-control <?= (isset($validation) && $validation->hasError('identifier')) ? 'is-invalid' : '' ?>" 
                                           id="identifier" 
                                           name="identifier" 
                                           value="<?= old('identifier') ?>" 
                                           placeholder="Enter your username or email"
                                           required>
                                    <?php if (isset($validation) && $validation->hasError('identifier')): ?>
                                        <div class="invalid-feedback">
                                            <?= $validation->getError('identifier') ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Password Field -->
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="bi bi-lock me-1"></i>
                                        Password
                                    </label>
                                    <div class="input-group">
                                        <input type="password" 
                                               class="form-control <?= (isset($validation) && $validation->hasError('password')) ? 'is-invalid' : '' ?>" 
                                               id="password" 
                                               name="password" 
                                               placeholder="Enter your password"
                                               required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="bi bi-eye" id="togglePasswordIcon"></i>
                                        </button>
                                        <?php if (isset($validation) && $validation->hasError('password')): ?>
                                            <div class="invalid-feedback">
                                                <?= $validation->getError('password') ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Remember Me -->
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        Remember me
                                    </label>
                                </div>
                                
                                <!-- Login Button -->
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>
                                        Sign In
                                    </button>
                                </div>
                                
                                <!-- Forgot Password Link -->
                                <div class="text-center">
                                    <a href="<?= base_url('dakoii/forgot-password') ?>" class="text-decoration-none">
                                        <small>Forgot your password?</small>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            <i class="bi bi-shield-check me-1"></i>
                            Secure access to CHS PNG Management System
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('togglePasswordIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }
        
        // Auto-focus on first input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('identifier').focus();
        });
    </script>
</body>
</html>
