<?php

namespace App\Libraries;

use App\Models\AdminUserModel;
use CodeIgniter\Session\Session;

class AdminAuth
{
    protected $session;
    protected $userModel;

    public function __construct()
    {
        $this->session = \Config\Services::session();
        $this->userModel = new AdminUserModel();
    }

    /**
     * Attempt to log in a user
     */
    public function attempt(string $identifier, string $password): bool
    {
        $user = $this->userModel->findByUsernameOrEmail($identifier);

        if (!$user) {
            return false;
        }

        if (!$this->userModel->verifyPassword($password, $user['password_hash'])) {
            return false;
        }

        if (!$user['is_active']) {
            return false;
        }

        // Set session data with admin_ prefix to avoid conflicts with dakoii portal
        $this->session->set([
            'admin_user_id' => $user['id'],
            'admin_username' => $user['username'],
            'admin_email' => $user['email'],
            'admin_first_name' => $user['first_name'],
            'admin_last_name' => $user['last_name'],
            'admin_role' => $user['role'],
            'admin_agency_id' => $user['agency_id'],
            'admin_logged_in' => true,
            'admin_last_activity' => time()
        ]);

        // Update last login (to be implemented in AdminUserModel)
        // $this->userModel->updateLastLogin($user['id']);

        return true;
    }

    /**
     * Check if user is logged in
     */
    public function check(): bool
    {
        return $this->session->get('admin_logged_in') === true;
    }

    /**
     * Get current user data
     */
    public function user(): ?array
    {
        if (!$this->check()) {
            return null;
        }

        return [
            'id' => $this->session->get('admin_user_id'),
            'username' => $this->session->get('admin_username'),
            'email' => $this->session->get('admin_email'),
            'first_name' => $this->session->get('admin_first_name'),
            'last_name' => $this->session->get('admin_last_name'),
            'role' => $this->session->get('admin_role'),
            'agency_id' => $this->session->get('admin_agency_id'),
        ];
    }

    /**
     * Get current user ID
     */
    public function id(): ?int
    {
        return $this->check() ? $this->session->get('admin_user_id') : null;
    }

    /**
     * Get current user's full name
     */
    public function fullName(): string
    {
        if (!$this->check()) {
            return '';
        }

        return $this->session->get('admin_first_name') . ' ' . $this->session->get('admin_last_name');
    }

    /**
     * Get current user's role
     */
    public function role(): ?string
    {
        return $this->check() ? $this->session->get('admin_role') : null;
    }

    /**
     * Get current user's agency ID
     */
    public function agencyId(): ?int
    {
        return $this->check() ? $this->session->get('admin_agency_id') : null;
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->check() && $this->role() === $role;
    }

    /**
     * Check if user is supervisor
     */
    public function isSupervisor(): bool
    {
        return $this->hasRole('supervisor');
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user has permission for specific action
     */
    public function hasPermission(string $action, string $resource = ''): bool
    {
        if (!$this->check()) {
            return false;
        }

        $role = $this->role();

        // Admin has all permissions
        if ($role === 'admin') {
            return true;
        }

        // Supervisor permissions
        if ($role === 'supervisor') {
            $supervisorPermissions = [
                'users.view', 'users.create', 'users.edit', 'users.delete',
                'agency_users.view', 'agency_users.assign', 'agency_users.transfer',
                'reports.view', 'reports.export',
                'dashboard.view'
            ];
            return in_array($action, $supervisorPermissions);
        }

        // User permissions (limited)
        if ($role === 'user') {
            $userPermissions = [
                'dashboard.view', 'profile.view', 'profile.edit'
            ];
            return in_array($action, $userPermissions);
        }

        return false;
    }

    /**
     * Log out the user
     */
    public function logout(): void
    {
        $this->session->remove([
            'admin_user_id',
            'admin_username',
            'admin_email',
            'admin_first_name',
            'admin_last_name',
            'admin_role',
            'admin_agency_id',
            'admin_logged_in',
            'admin_last_activity'
        ]);

        $this->session->regenerate(true);
    }

    /**
     * Validate session timeout
     */
    public function validateSession(): bool
    {
        if (!$this->check()) {
            return false;
        }

        $lastActivity = $this->session->get('admin_last_activity');
        $timeout = 1800; // 30 minutes

        if ($lastActivity && (time() - $lastActivity > $timeout)) {
            $this->logout();
            return false;
        }

        $this->session->set('admin_last_activity', time());
        return true;
    }

    /**
     * Generate remember token (for future use)
     */
    public function generateRememberToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Route user based on agency_id
     * If agency_id is null/empty: Admin Portal
     * If agency_id has value: Agency Portal (future implementation)
     */
    public function getRedirectUrl(): string
    {
        $agencyId = $this->agencyId();
        
        if (empty($agencyId)) {
            // No agency assigned = Admin Portal
            return '/admin/dashboard';
        } else {
            // Agency assigned = Agency Portal (future)
            return '/agency/dashboard';
        }
    }
}
