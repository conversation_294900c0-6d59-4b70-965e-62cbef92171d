# Dakoii Portal - System Features & User Stories

## System Overview
The Dakoii Portal serves as the super administrator interface for the CHS PNG employee management system, providing system-wide control and foundational setup capabilities.

---

## 🎯 Core System Features

### 1. **Authentication & Access Control**
- Dedicated login route `/dakoii`
- Secure authentication using `dakoii_users` table
- Session management with elevated privileges
- Multi-factor authentication support
- Password policy enforcement

### 2. **Dark Theme Interface**
- Modern dark UI template
- Eye-strain reduction for extended use
- Professional administrative appearance
- Responsive design for desktop and mobile
- Accessibility compliance

### 3. **Organization User Management**
- CRUD operations for admin users
- Role-based permission assignment
- User status management (active/inactive)
- Admin user authentication setup

### 4. **Agency Management**
- Complete agency lifecycle management
- Geographic and operational categorization
- Agency hierarchy and relationships
- Status tracking and reporting

### 5. **System Administration**
- Global system settings
- Audit trail and logging
- Data backup and recovery
- Performance monitoring

---

## 👤 User Stories

### **Authentication & Security**

#### Story #1: Super Admin Login
**As a** Dakoii super administrator  
**I want to** access the system through a dedicated login portal  
**So that** I can manage the entire CHS PNG system securely  

**Acceptance Criteria:**
- Access login form at `/dakoii` route
- Authenticate using credentials stored in `dakoii_users` table
- Display dark-themed login interface
- Redirect to dashboard upon successful authentication
- Show appropriate error messages for failed login attempts
- Support password reset functionality

#### Story #2: Secure Session Management
**As a** Dakoii super administrator  
**I want to** have secure session management with appropriate timeouts  
**So that** unauthorized access is prevented when I step away  

**Acceptance Criteria:**
- Automatic session timeout after 30 minutes of inactivity
- Session extension prompt before timeout
- Secure logout functionality
- Protection against session hijacking
- Clear session data on logout

---

### **Organization User Management**

#### Story #3: Create Admin Users
**As a** Dakoii super administrator  
**I want to** create new admin users for the organization  
**So that** I can delegate administrative responsibilities  

**Acceptance Criteria:**
- Create new admin user with required details (name, email, phone, role)
- Assign specific permissions and access levels
- Generate secure temporary passwords
- Send welcome email with login instructions
- Set user status (active/pending/inactive)
- Validate unique email addresses

#### Story #4: Manage Admin User Roles
**As a** Dakoii super administrator  
**I want to** assign and modify admin user roles and permissions  
**So that** each admin has appropriate access to system functions  

**Acceptance Criteria:**
- Define role types (HR Manager, Regional Supervisor, System Admin)
- Assign multiple roles to single user if needed
- Modify permissions for existing users
- View complete permission matrix
- Audit trail for permission changes

#### Story #5: Update Admin User Information
**As a** Dakoii super administrator  
**I want to** update existing admin user details  
**So that** user information remains current and accurate  

**Acceptance Criteria:**
- Edit user personal information
- Update contact details
- Modify reporting relationships
- Change user status (activate/deactivate)
- Update role assignments
- Log all changes with timestamp and reason

#### Story #6: Delete Admin Users
**As a** Dakoii super administrator  
**I want to** deactivate or remove admin users from the system  
**So that** former employees cannot access the system  

**Acceptance Criteria:**
- Soft delete option (deactivate) vs hard delete
- Transfer ownership of created records to another admin
- Confirmation dialog before deletion
- Audit log of deleted users
- Ability to reactivate soft-deleted users

---

### **Agency Management**

#### Story #7: Create New Agency
**As a** Dakoii super administrator  
**I want to** create new health facility agencies in the system  
**So that** each CHS facility can be managed independently  

**Acceptance Criteria:**
- Enter agency details (name, type, location, contact information)
- Assign unique agency codes/IDs
- Set agency category (Hospital, Clinic, Health Center, etc.)
- Define geographic location (Province, District)
- Set operational status (Active, Under Construction, Planned)
- Upload facility photos and documentation

#### Story #8: Configure Agency Hierarchy
**As a** Dakoii super administrator  
**I want to** establish relationships between agencies  
**So that** reporting structures and oversight are properly defined  

**Acceptance Criteria:**
- Define parent-child relationships between facilities
- Assign regional oversight responsibilities
- Set up referral networks between facilities
- Configure reporting hierarchies
- Map geographic coverage areas

#### Story #9: Update Agency Information
**As a** Dakoii super administrator  
**I want to** modify existing agency details  
**So that** facility information remains current and accurate  

**Acceptance Criteria:**
- Edit facility contact information
- Update operational status
- Modify facility categories and services offered
- Change geographic assignments
- Update facility capacity and staffing levels
- Log all changes with audit trail

#### Story #10: Manage Agency Status
**As a** Dakoii super administrator  
**I want to** activate, deactivate, or temporarily suspend agencies  
**So that** system access reflects actual operational status  

**Acceptance Criteria:**
- Change agency status (Active, Inactive, Suspended, Under Maintenance)
- Set effective dates for status changes
- Notify affected users of status changes
- Prevent access to suspended agencies
- Maintain historical status records

---

### **System Administration**

#### Story #11: View System Dashboard
**As a** Dakoii super administrator  
**I want to** see a comprehensive system overview dashboard  
**So that** I can monitor overall system health and usage  

**Acceptance Criteria:**
- Display total number of agencies, admins, and employees
- Show system performance metrics
- View recent activity logs
- Display alerts and notifications
- Show geographic distribution of agencies
- Present usage statistics and trends

#### Story #12: Generate System Reports
**As a** Dakoii super administrator  
**I want to** generate comprehensive reports across all agencies  
**So that** I can analyze system-wide performance and compliance  

**Acceptance Criteria:**
- Generate agency status reports
- Create user activity reports
- Export data in multiple formats (PDF, Excel, CSV)
- Schedule automated reports
- Filter reports by date range, region, or agency type
- Send reports via email

#### Story #13: Manage System Settings
**As a** Dakoii super administrator  
**I want to** configure global system settings  
**So that** the system operates according to organizational policies  

**Acceptance Criteria:**
- Configure password policies
- Set session timeout durations
- Manage email templates and notifications
- Define system-wide validation rules
- Set up backup schedules
- Configure integration settings

#### Story #14: View Audit Logs
**As a** Dakoii super administrator  
**I want to** access comprehensive audit trails  
**So that** I can track all system changes and maintain accountability  

**Acceptance Criteria:**
- View all user actions across the system
- Filter logs by user, date, action type
- Export audit logs for compliance
- Search logs by specific criteria
- View detailed change history
- Monitor failed login attempts

---

## 🗄️ Database Schema Requirements

### CodeIgniter 4 Migration Structure

#### dakoii_users Table Migration
```php
<?php
// app/Database/Migrations/YYYY-MM-DD-HHMMSS_CreateDakoiiUsersTable.php

namespace App\Database\Migrations;
use CodeIgniter\Database\Migration;

class CreateDakoiiUsersTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'username' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'unique' => true,
            ],
            'email' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'unique' => true,
            ],
            'password_hash' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'first_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'last_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'phone_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
            ],
            'last_login_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'two_factor_enabled' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('username');
        $this->forge->addUniqueKey('email');
        $this->forge->createTable('dakoii_users');
    }

    public function down()
    {
        $this->forge->dropTable('dakoii_users');
    }
}
```

#### Additional Required Tables

##### agencies Table Migration
```php
// For agency management functionality
$this->forge->addField([
    'id' => ['type' => 'INT', 'auto_increment' => true],
    'agency_code' => ['type' => 'VARCHAR', 'constraint' => 20, 'unique' => true],
    'name' => ['type' => 'VARCHAR', 'constraint' => 255],
    'address' => ['type' => 'TEXT', 'null' => true],
    'phone' => ['type' => 'VARCHAR', 'constraint' => 20, 'null' => true],
    'email' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true],
    'status' => ['type' => 'ENUM', 'constraint' => ['active', 'inactive', 'suspended'], 'default' => 'active'],
    'created_at' => ['type' => 'DATETIME', 'null' => true],
    'updated_at' => ['type' => 'DATETIME', 'null' => true],
    'created_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
    'updated_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
    'deleted_at' => ['type' => 'DATETIME', 'null' => true],
    'deleted_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
]);
```

##### admin_users Table Migration
```php
// For organization admin user management
$this->forge->addField([
    'id' => ['type' => 'INT', 'auto_increment' => true],
    'username' => ['type' => 'VARCHAR', 'constraint' => 50, 'unique' => true],
    'email' => ['type' => 'VARCHAR', 'constraint' => 100, 'unique' => true],
    'password_hash' => ['type' => 'VARCHAR', 'constraint' => 255],
    'first_name' => ['type' => 'VARCHAR', 'constraint' => 50],
    'last_name' => ['type' => 'VARCHAR', 'constraint' => 50],
    'phone_number' => ['type' => 'VARCHAR', 'constraint' => 20, 'null' => true],
    'role' => ['type' => 'ENUM', 'constraint' => ['supervisor', 'user', 'admin']],
    'agency_id' => ['type' => 'INT', 'null' => true],
    'is_active' => ['type' => 'TINYINT', 'constraint' => 1, 'default' => 1],
    'created_at' => ['type' => 'DATETIME', 'null' => true],
    'updated_at' => ['type' => 'DATETIME', 'null' => true],
    'created_by' => ['type' => 'INT', 'null' => true],
]);
```

---

## 🎨 UI/UX Requirements

### Dark Theme Specifications with CHS Branding
- **Primary Background**: Dark navy/charcoal (#1a1a1a, #2d3748)
- **Secondary Background**: Lighter gray panels (#374151, #4a5568)
- **Text Colors**: Light gray/white (#f7fafc, #e2e8f0)
- **CHS Brand Colors**:
  - Deep Cerulean Blue (#1A4E8C) - Primary accent, buttons, links
  - Engine Red (#C8102E) - Alerts, important actions, highlights
  - White (#FFFFFF) - Text on dark backgrounds
  - Black (#000000) - Text on light elements
- **Interactive Elements**: High contrast for accessibility
- **Form Elements**: Dark styled inputs with proper focus indicators

### Bootstrap 5 Implementation
- **CSS Framework**: Bootstrap 5 with custom dark theme overrides
- **Grid System**: Responsive 12-column grid layout
- **Components**: Dark-themed Bootstrap components
- **Utilities**: Custom utility classes for CHS branding
- **Icons**: Bootstrap Icons or Font Awesome for consistency

### Navigation Structure
- **Sidebar Navigation**: Bootstrap offcanvas collapsible dark sidebar with icons
- **Top Bar**: Bootstrap navbar with user profile, notifications, logout
- **Breadcrumbs**: Bootstrap breadcrumb component for navigation path
- **Search Functionality**: Bootstrap search input with live results

### CodeIgniter 4 View Structure
```php
// app/Views/layouts/dakoii_layout.php - Main layout template
// app/Views/dakoii/ - All Dakoii portal views
//   ├── auth/
//   │   ├── login.php
//   │   └── forgot_password.php
//   ├── dashboard/
//   │   └── index.php
//   ├── admin_users/
//   │   ├── index.php
//   │   ├── create.php
//   │   ├── edit.php
//   │   └── view.php
//   ├── agencies/
//   │   ├── index.php
//   │   ├── create.php
//   │   ├── edit.php
//   │   └── view.php
//   └── partials/
//       ├── sidebar.php
//       ├── topbar.php
//       └── footer.php
```

---

## 🔐 Security Requirements

### CodeIgniter 4 Security Implementation

#### Access Control
- Super admin privileges across all system functions
- Separate authentication table (`dakoii_users`)
- Role-based access control using CI4 Filters
- Session-based authentication with CI4 Session Library
- Input validation using CI4 Validation Library
- XSS filtering with CI4 Security Library

#### Authentication & Authorization
```php
// app/Filters/DakoiiAuthFilter.php - Custom authentication filter
// app/Libraries/DakoiiAuth.php - Authentication library
// Password hashing using PHP password_hash() and password_verify()
// Session management with CI4 session configuration
```

#### Data Protection
- **Password Storage**: PHP password_hash() with PASSWORD_DEFAULT (minimum 4 characters)
- **Session Security**: CI4 Session with secure cookies, regeneration
- **CSRF Protection**: Built-in CI4 CSRF filter enabled
- **XSS Prevention**: CI4 Security::xss_clean() for user inputs
- **SQL Injection**: CI4 Query Builder with parameter binding
- **Input Validation**: CI4 Validation rules for all forms

#### Security Configuration
```php
// app/Config/Security.php
public string $csrfProtection = 'cookie';
public bool $tokenRandomize = true;
public bool $regenerate = true;

// app/Config/Session.php
public string $driver = 'CodeIgniter\Session\Handlers\FileHandler';
public string $cookieName = 'dakoii_session';
public int $expiration = 1800; // 30 minutes
public bool $matchIP = true;
public bool $regenerateDestroy = true;
```

#### Route Protection
```php
// app/Config/Routes.php
$routes->group('dakoii', ['filter' => 'dakoii_auth'], function($routes) {
    // Protected routes here
});
```

---

## 📱 Technical Requirements

### Framework & Technology Stack
- **Backend**: CodeIgniter 4 (PHP 8.1+)
- **Database**: MySQL/MySQLi with CodeIgniter 4 Query Builder
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5 with custom dark theme
- **Authentication**: CodeIgniter 4 Session Library
- **Security**: Built-in CSRF protection, XSS filtering
- **File Handling**: CodeIgniter 4 File Upload Library
- **Environment**: XAMPP (Apache, MySQL, PHP)

### Performance
- Dashboard load time < 2 seconds
- Search results returned < 1 second
- Support for concurrent super admin sessions
- Efficient pagination using CodeIgniter 4 Pager
- Database query optimization with CI4 Query Builder

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design with Bootstrap 5
- No PWA or offline capabilities required
- Cross-browser compatibility testing

### CodeIgniter 4 Architecture
- **MVC Pattern**: Strict separation of Models, Views, Controllers
- **RESTful Routes**: Standard CI4 routing conventions
- **Models**: Extend CodeIgniter\Model with built-in validation
- **Controllers**: Extend BaseController with shared functionality
- **Views**: Template-based rendering with CI4 View Parser
- **Migrations**: Database schema management
- **Seeders**: Initial data population

### Integration Points
- API endpoints for other portals using CI4 API Response Trait
- Email service integration via CI4 Email Library
- File upload to public/uploads directory
- Session-based authentication across portals

---

## 🏗️ Implementation Roadmap

### Phase 1: Foundation Setup
1. **Database Setup**
   - Create migrations for `dakoii_users`, `agencies`, `admin_users` tables
   - Run migrations: `php spark migrate`
   - Create seeders for initial data

2. **Authentication System**
   - Create DakoiiAuth library
   - Implement DakoiiAuthFilter
   - Set up login/logout controllers
   - Configure session management

3. **Base Structure**
   - Create Dakoii base controller
   - Set up routing for `/dakoii` routes
   - Create main layout template
   - Implement dark theme CSS

### Phase 2: Core Functionality
1. **Dashboard**
   - System overview with statistics
   - Recent activity display
   - Quick action buttons

2. **Admin User Management**
   - CRUD operations for admin users
   - Role assignment functionality
   - User status management

3. **Agency Management**
   - CRUD operations for agencies
   - Agency hierarchy management
   - Status tracking

### Phase 3: Advanced Features
1. **System Administration**
   - Audit logging
   - System settings management
   - Report generation

2. **Security Enhancements**
   - Two-factor authentication
   - Password policy enforcement
   - Session timeout management

### File Structure
```
app/
├── Controllers/
│   └── Dakoii/
│       ├── DakoiiBaseController.php
│       ├── AuthController.php
│       ├── DashboardController.php
│       ├── AdminUsersController.php
│       ├── AgenciesController.php
│       └── SystemController.php
├── Models/
│   ├── DakoiiUserModel.php
│   ├── AdminUserModel.php
│   ├── AgencyModel.php
│   └── AuditLogModel.php
├── Views/
│   ├── layouts/
│   │   └── dakoii_layout.php
│   └── dakoii/
│       ├── auth/
│       ├── dashboard/
│       ├── admin_users/
│       ├── agencies/
│       └── partials/
├── Filters/
│   └── DakoiiAuthFilter.php
├── Libraries/
│   └── DakoiiAuth.php
└── Database/
    ├── Migrations/
    │   ├── CreateDakoiiUsersTable.php
    │   ├── CreateAgenciesTable.php
    │   └── CreateAdminUsersTable.php
    └── Seeds/
        ├── DakoiiUserSeeder.php
        └── AgencySeeder.php

public/
└── assets/
    ├── css/
    │   └── dakoii-dark-theme.css
    ├── js/
    │   └── dakoii-app.js
    └── images/
        ├── dakoii-logo.png
        └── chs-logo.png
```