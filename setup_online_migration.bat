@echo off
echo ========================================
echo Online Database Migration Setup
echo ========================================
echo.

echo This script will help you migrate your local database to the online server.
echo.

echo Prerequisites:
echo 1. Make sure you have SSH client installed (Windows 10/11 has it built-in)
echo 2. Make sure XAMPP is running
echo 3. Have the SSH password ready: dakoiianzii
echo.

echo Step 1: Testing SSH connection...
echo.
echo The SSH tunnel command is:
echo ssh -L 3307:127.0.0.1:3306 <EMAIL> -N
echo.
echo This will:
echo - Forward local port 3307 to remote MySQL port 3306
echo - Keep the connection open (-N flag)
echo - You'll need to enter the SSH password: dakoiianzii
echo.

set /p continue="Press Enter to start SSH tunnel (or Ctrl+C to cancel)..."

echo.
echo Starting SSH tunnel...
echo IMPORTANT: Keep this window open! The tunnel must stay active.
echo Open a NEW command prompt window to run the migration commands.
echo.
echo SSH Password: dakoiianzii
echo.

start "SSH Tunnel - Keep Open" cmd /k "echo SSH Tunnel Active - DO NOT CLOSE THIS WINDOW && echo. && ssh -L 3307:127.0.0.1:3306 <EMAIL> -N"

echo.
echo SSH tunnel started in new window.
echo.
echo Next steps:
echo 1. Enter the SSH password (dakoiianzii) in the SSH tunnel window
echo 2. Open a NEW command prompt
echo 3. Navigate to your project directory: cd c:\xampp\htdocs\chealthwokman
echo 4. Run the migration: php migrate_to_online.php all
echo.

echo Migration Commands:
echo   php migrate_to_online.php test     - Test connection
echo   php migrate_to_online.php migrate  - Run migrations only
echo   php migrate_to_online.php seed     - Run seeder only  
echo   php migrate_to_online.php all      - Complete migration
echo.

pause
