<!-- <PERSON>er -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Agencies</h1>
        <p class="text-muted mb-0">Manage organization agencies and their information</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/agencies/create') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            Create New Agency
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Total Agencies</div>
                        <div class="h2 mb-0 text-light"><?= number_format($stats['total']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building stats-icon text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Active</div>
                        <div class="h2 mb-0 text-success"><?= number_format($stats['active']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle stats-icon text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Inactive</div>
                        <div class="h2 mb-0 text-warning"><?= number_format($stats['inactive']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-pause-circle stats-icon text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-uppercase text-muted fw-bold small">Suspended</div>
                        <div class="h2 mb-0 text-danger"><?= number_format($stats['suspended']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-x-circle stats-icon text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('dakoii/agencies') ?>" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search</label>
                <input type="text" 
                       class="form-control" 
                       id="search" 
                       name="search" 
                       value="<?= esc($search) ?>" 
                       placeholder="Search by agency code, name, or email...">
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search me-1"></i>
                    Search
                </button>
                <a href="<?= base_url('dakoii/agencies') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Agencies Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="bi bi-building me-2"></i>
            Agencies List
        </h5>
        <span class="badge bg-primary">
            Total: <?= count($agencies) ?> agencies
        </span>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($agencies)): ?>
        <div class="table-responsive">
            <table class="table table-dark table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>Agency Code</th>
                        <th>Name</th>
                        <th>Contact</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($agencies as $agency): ?>
                    <tr>
                        <td>
                            <span class="badge bg-info font-monospace">
                                <?= esc($agency['agency_code']) ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                    <i class="bi bi-building text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= esc($agency['name']) ?></div>
                                    <?php if (!empty($agency['address'])): ?>
                                    <small class="text-muted"><?= esc(substr($agency['address'], 0, 50)) ?><?= strlen($agency['address']) > 50 ? '...' : '' ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <?php if (!empty($agency['phone'])): ?>
                                <div><i class="bi bi-telephone me-1"></i><?= esc($agency['phone']) ?></div>
                                <?php endif; ?>
                                <?php if (!empty($agency['email'])): ?>
                                <div><i class="bi bi-envelope me-1"></i><a href="mailto:<?= esc($agency['email']) ?>" class="text-decoration-none"><?= esc($agency['email']) ?></a></div>
                                <?php endif; ?>
                                <?php if (empty($agency['phone']) && empty($agency['email'])): ?>
                                <span class="text-muted">No contact info</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php
                            $statusClass = 'bg-secondary';
                            if ($agency['status'] === 'active') $statusClass = 'bg-success';
                            elseif ($agency['status'] === 'inactive') $statusClass = 'bg-warning';
                            elseif ($agency['status'] === 'suspended') $statusClass = 'bg-danger';
                            ?>
                            <span class="badge <?= $statusClass ?>">
                                <?= ucfirst(esc($agency['status'])) ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($agency['created_at'])) ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?= base_url('dakoii/agencies/' . $agency['id']) ?>" 
                                   class="btn btn-outline-info" 
                                   title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="<?= base_url('dakoii/agencies/' . $agency['id'] . '/edit') ?>" 
                                   class="btn btn-outline-warning" 
                                   title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="POST" 
                                      action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/delete') ?>" 
                                      class="d-inline"
                                      onsubmit="return confirm('Are you sure you want to delete this agency?')">
                                    <?= csrf_field() ?>
                                    <button type="submit" 
                                            class="btn btn-outline-danger" 
                                            title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($pager->getPageCount() > 1): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing <?= $pager->getFirstPage() ?> to <?= $pager->getLastPage() ?> of <?= $pager->getTotal() ?> results
                </div>
                <div>
                    <?= $pager->links() ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="bi bi-building display-1 text-muted"></i>
            <h4 class="mt-3 text-muted">No Agencies Found</h4>
            <?php if (!empty($search)): ?>
                <p class="text-muted">No agencies match your search criteria.</p>
                <a href="<?= base_url('dakoii/agencies') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>
                    View All Agencies
                </a>
            <?php else: ?>
                <p class="text-muted">Get started by creating your first agency.</p>
                <a href="<?= base_url('dakoii/agencies/create') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Create First Agency
                </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
