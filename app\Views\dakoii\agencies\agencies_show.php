<!-- <PERSON>er -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Agency Details</h1>
        <p class="text-muted mb-0">View agency information and settings</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/agencies/' . $agency['id'] . '/edit') ?>" class="btn btn-warning me-2">
            <i class="bi bi-pencil me-2"></i>
            Edit Agency
        </a>
        <a href="<?= base_url('dakoii/agencies') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to List
        </a>
    </div>
</div>

<!-- Agency Details -->
<div class="row">
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-building me-2"></i>
                    Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Agency Code</label>
                            <div class="h5">
                                <span class="badge bg-info font-monospace fs-6">
                                    <?= esc($agency['agency_code']) ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Agency Name</label>
                            <div class="h5 text-light">
                                <?= esc($agency['name']) ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Status</label>
                            <div>
                                <?php
                                $statusClass = 'bg-secondary';
                                $statusLabel = ucfirst($agency['status']);
                                if ($agency['status'] === 'active') $statusClass = 'bg-success';
                                elseif ($agency['status'] === 'inactive') $statusClass = 'bg-warning';
                                elseif ($agency['status'] === 'suspended') $statusClass = 'bg-danger';
                                ?>
                                <span class="badge <?= $statusClass ?> fs-6">
                                    <?= esc($statusLabel) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone Number</label>
                            <div class="text-light">
                                <?php if (!empty($agency['phone'])): ?>
                                    <a href="tel:<?= esc($agency['phone']) ?>" class="text-decoration-none">
                                        <i class="bi bi-telephone me-2"></i><?= esc($agency['phone']) ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Email Address</label>
                            <div class="text-light">
                                <?php if (!empty($agency['email'])): ?>
                                    <a href="mailto:<?= esc($agency['email']) ?>" class="text-decoration-none">
                                        <i class="bi bi-envelope me-2"></i><?= esc($agency['email']) ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Address</label>
                            <div class="text-light">
                                <?php if (!empty($agency['address'])): ?>
                                    <div style="white-space: pre-line;"><?= esc($agency['address']) ?></div>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Status Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Current Status: <?= ucfirst(esc($agency['status'])) ?></h6>
                        
                        <?php if ($agency['status'] === 'active'): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>
                                <strong>Active Agency</strong><br>
                                This agency is fully operational and can receive assignments.
                            </div>
                        <?php elseif ($agency['status'] === 'inactive'): ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-pause-circle me-2"></i>
                                <strong>Inactive Agency</strong><br>
                                This agency is temporarily not operational. No new assignments should be made.
                            </div>
                        <?php elseif ($agency['status'] === 'suspended'): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-x-circle me-2"></i>
                                <strong>Suspended Agency</strong><br>
                                This agency's operations are suspended. All activities are on hold.
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">Available Actions</h6>
                        <div class="d-grid gap-2">
                            <?php if ($agency['status'] !== 'active'): ?>
                            <form method="POST" action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/update') ?>" class="d-inline">
                                <?= csrf_field() ?>
                                <input type="hidden" name="agency_code" value="<?= esc($agency['agency_code']) ?>">
                                <input type="hidden" name="name" value="<?= esc($agency['name']) ?>">
                                <input type="hidden" name="address" value="<?= esc($agency['address']) ?>">
                                <input type="hidden" name="phone" value="<?= esc($agency['phone']) ?>">
                                <input type="hidden" name="email" value="<?= esc($agency['email']) ?>">
                                <input type="hidden" name="status" value="active">
                                <button type="submit" class="btn btn-success btn-sm w-100">
                                    <i class="bi bi-play-circle me-2"></i>
                                    Activate Agency
                                </button>
                            </form>
                            <?php endif; ?>
                            
                            <?php if ($agency['status'] !== 'inactive'): ?>
                            <form method="POST" action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/update') ?>" class="d-inline">
                                <?= csrf_field() ?>
                                <input type="hidden" name="agency_code" value="<?= esc($agency['agency_code']) ?>">
                                <input type="hidden" name="name" value="<?= esc($agency['name']) ?>">
                                <input type="hidden" name="address" value="<?= esc($agency['address']) ?>">
                                <input type="hidden" name="phone" value="<?= esc($agency['phone']) ?>">
                                <input type="hidden" name="email" value="<?= esc($agency['email']) ?>">
                                <input type="hidden" name="status" value="inactive">
                                <button type="submit" class="btn btn-warning btn-sm w-100" 
                                        onclick="return confirm('Are you sure you want to deactivate this agency?')">
                                    <i class="bi bi-pause-circle me-2"></i>
                                    Deactivate Agency
                                </button>
                            </form>
                            <?php endif; ?>
                            
                            <?php if ($agency['status'] !== 'suspended'): ?>
                            <form method="POST" action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/update') ?>" class="d-inline">
                                <?= csrf_field() ?>
                                <input type="hidden" name="agency_code" value="<?= esc($agency['agency_code']) ?>">
                                <input type="hidden" name="name" value="<?= esc($agency['name']) ?>">
                                <input type="hidden" name="address" value="<?= esc($agency['address']) ?>">
                                <input type="hidden" name="phone" value="<?= esc($agency['phone']) ?>">
                                <input type="hidden" name="email" value="<?= esc($agency['email']) ?>">
                                <input type="hidden" name="status" value="suspended">
                                <button type="submit" class="btn btn-danger btn-sm w-100" 
                                        onclick="return confirm('Are you sure you want to suspend this agency?')">
                                    <i class="bi bi-x-circle me-2"></i>
                                    Suspend Agency
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Activity Log (Placeholder) -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity me-2"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="bi bi-clock-history display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted">Activity Tracking</h6>
                    <p class="text-muted">Activity logging will be implemented in future updates.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('dakoii/agencies/' . $agency['id'] . '/edit') ?>" class="btn btn-warning">
                        <i class="bi bi-pencil me-2"></i>
                        Edit Agency
                    </a>
                    
                    <a href="<?= base_url('dakoii/agencies/create') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Create New Agency
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Agency Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    Agency Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Created</small>
                    <div class="fw-bold"><?= date('M j, Y', strtotime($agency['created_at'])) ?></div>
                    <small class="text-muted"><?= date('g:i A', strtotime($agency['created_at'])) ?></small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Last Updated</small>
                    <div class="fw-bold"><?= date('M j, Y', strtotime($agency['updated_at'])) ?></div>
                    <small class="text-muted"><?= date('g:i A', strtotime($agency['updated_at'])) ?></small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Agency Age</small>
                    <div class="fw-bold">
                        <?php
                        $created = new DateTime($agency['created_at']);
                        $now = new DateTime();
                        $diff = $now->diff($created);
                        echo $diff->days . ' days';
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Danger Zone -->
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Danger Zone
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    Permanently delete this agency. This action cannot be undone.
                </p>
                <form method="POST" 
                      action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/delete') ?>" 
                      onsubmit="return confirm('Are you sure you want to delete this agency? This action cannot be undone.')">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger btn-sm">
                        <i class="bi bi-trash me-2"></i>
                        Delete Agency
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
