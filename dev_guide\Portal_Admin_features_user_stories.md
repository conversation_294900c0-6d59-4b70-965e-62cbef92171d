# Admin Portal - System Features & User Stories

## System Overview
The Admin Portal serves as the organization management interface for the CHS PNG employee management system, providing centralized user management and oversight capabilities for supervisors and regular users across all agencies.

## 📋 Current Implementation Status

### ✅ Completed Components
- **Database Structure**: All required tables (admin_users, agencies, dakoii_users) created and migrated
- **Dakoii Portal**: Super administrator portal fully implemented with dark theme
- **Core Models**: AdminUserModel, AgencyModel, DakoiiUserModel implemented
- **Authentication Pattern**: DakoiiAuth library and filter established as template
- **Tech Stack**: CodeIgniter 4, Bootstrap 5, MySQL, XAMPP environment configured

### 🚧 To Be Implemented
- **Admin Portal**: Organization admin interface (this document's focus)
- **AdminAuth Library**: Authentication system for admin portal
- **AdminAuthFilter**: Access control for admin routes
- **Light Theme**: CHS-branded UI for admin portal
- **Admin Controllers**: User management, dashboard, reports functionality

### 🏗️ Architecture Pattern
This system follows a multi-portal architecture:
1. **Dakoii Portal** (`/dakoii`) - Super admin with system-wide access
2. **Admin Portal** (`/admin`) - Organization admin with agency-specific access
3. **Agency Portal** (future) - User-level access to agency functions

---

## 🎯 Core System Features

### 1. **Authentication & Access Control**
- Shared login form with Agency Portal (unified authentication)
- Authentication using `admin_users` table
- Login routing based on agency_id (empty = Admin Portal, populated = Agency Portal)
- Session management with appropriate privileges
- Role-based access control (Supervisor, User roles only)

### 2. **Professional Theme Interface**
- Clean, professional template based on landing page theme
- CHS branding consistency
- Responsive design for desktop and mobile
- Modern UI components with accessibility compliance
- Light theme with CHS brand colors

### 3. **User Management**
- CRUD operations for Supervisors and Users (excluding Admin users)
- Agency user linking and management
- Role assignment and permission control
- User status management (active/inactive)
- Reporting hierarchy management

### 4. **Agency User Coordination**
- Link users to specific agencies
- Manage agency-based user assignments
- Oversee cross-agency user relationships
- Monitor agency user activities

### 5. **Reporting & Analytics**
- User activity monitoring
- Agency user distribution reports
- Supervisor oversight dashboards
- System usage analytics

---

## 👤 User Stories

### **Authentication & Access Control**

#### Story #1: Unified Login Access
**As an** Admin Portal user  
**I want to** access the system through a shared login form  
**So that** I can authenticate and be routed to the appropriate portal based on my role  

**Acceptance Criteria:**
- Click login from landing page to access shared login form
- Authenticate using credentials from `admin_users` table
- System checks agency_id field after successful authentication
- If agency_id is empty/null, redirect to Admin Portal dashboard
- If agency_id has value, redirect to Agency Portal dashboard
- Display appropriate error messages for failed login attempts
- Support password reset functionality through shared form

#### Story #2: Role-Based Access Control
**As an** Admin Portal user  
**I want to** have access restricted based on my assigned role  
**So that** I can only perform actions appropriate to my position  

**Acceptance Criteria:**
- Supervisor role has full CRUD access to users and agency assignments
- User role has limited access (view users, manage own profile)
- Admin users are not accessible through this portal
- Role-based navigation menu display
- Appropriate error messages for unauthorized access attempts

---

### **Dashboard & Overview**

#### Story #3: Admin Portal Dashboard
**As an** Admin Portal user  
**I want to** see a comprehensive dashboard upon login  
**So that** I can quickly understand system status and my responsibilities  

**Acceptance Criteria:**
- Display total count of supervisors and users
- Show agency user distribution statistics
- Display recent user activities and registrations
- Show pending user approvals or actions required
- Quick access buttons to common functions
- Professional layout matching landing page theme

#### Story #4: System Overview Widgets
**As a** Supervisor  
**I want to** see key metrics and alerts on the dashboard  
**So that** I can monitor system health and user management needs  

**Acceptance Criteria:**
- Widget showing users by agency distribution
- Alert notifications for inactive users or pending assignments
- Recent activity feed for user management actions
- Quick statistics on user roles and statuses
- Links to detailed reports and management functions

---

### **User Management (CRUD Operations)**

#### Story #5: Create New Users
**As a** Supervisor  
**I want to** create new supervisor and user accounts  
**So that** I can onboard new personnel into the system  

**Acceptance Criteria:**
- Create user form with required fields (name, email, phone, role)
- Role selection limited to Supervisor and User only
- Generate secure temporary passwords
- Option to link user to specific agency during creation
- Send welcome email with login credentials
- Validate unique email addresses
- Set initial user status (active/pending)

#### Story #6: View User List
**As an** Admin Portal user  
**I want to** view a list of all supervisors and users  
**So that** I can see who has access to the system  

**Acceptance Criteria:**
- Display paginated list of users (exclude admin users)
- Show user details: name, email, role, agency assignment, status
- Search functionality by name, email, or role
- Filter options by role, agency, or status
- Sort options by name, role, created date
- Quick action buttons (edit, deactivate, view details)

#### Story #7: Edit User Information
**As a** Supervisor  
**I want to** update existing user details  
**So that** I can keep user information current and accurate  

**Acceptance Criteria:**
- Edit user personal information (name, email, phone)
- Modify role assignment (Supervisor ↔ User)
- Update agency assignments
- Change user status (activate/deactivate)
- Update reporting relationships
- Log all changes with timestamp and modifier
- Send notification email for significant changes

#### Story #8: Delete/Deactivate Users
**As a** Supervisor  
**I want to** deactivate or remove users from the system  
**So that** former personnel cannot access the system  

**Acceptance Criteria:**
- Soft delete option (deactivate) vs hard delete
- Confirmation dialog before deletion/deactivation
- Transfer user assignments to another supervisor
- Audit log of deleted/deactivated users
- Option to reactivate deactivated users
- Prevent deletion of users with active assignments

---

### **Agency User Management**

#### Story #9: Link Users to Agencies
**As a** Supervisor  
**I want to** assign users to specific agencies  
**So that** users can access the appropriate agency portal  

**Acceptance Criteria:**
- Select agency from dropdown list of active agencies
- Link multiple users to same agency in batch operations
- View current agency assignments in user list
- Ability to change agency assignments
- Validation to ensure agency exists and is active
- Audit trail for agency assignment changes

#### Story #10: Manage Agency User Distribution
**As a** Supervisor  
**I want to** view and manage how users are distributed across agencies  
**So that** I can ensure proper coverage and access control  

**Acceptance Criteria:**
- Display agency-wise user distribution report
- Show unassigned users (no agency linkage)
- Identify agencies with no assigned users
- Bulk assignment tools for multiple users
- Visual representation of user distribution (charts/graphs)
- Export agency user distribution reports

#### Story #11: Transfer Users Between Agencies
**As a** Supervisor  
**I want to** transfer users from one agency to another  
**So that** I can accommodate organizational changes and staff movements  

**Acceptance Criteria:**
- Select user and change agency assignment
- Confirm transfer with appropriate notifications
- Maintain history of agency assignments
- Update user access permissions automatically
- Notify both old and new agency administrators
- Validate target agency is active and accepts transfers

---

### **Reporting Hierarchy Management**

#### Story #12: Set Reporting Relationships
**As a** Supervisor  
**I want to** define who reports to whom in the organization  
**So that** proper oversight and approval workflows are established  

**Acceptance Criteria:**
- Assign supervisor relationships for users
- Prevent circular reporting relationships
- Display organizational chart view
- Update reporting relationships as needed
- Maintain history of reporting changes
- Validate supervisor has appropriate permissions

#### Story #13: View Reporting Structure
**As an** Admin Portal user  
**I want to** see the current reporting hierarchy  
**So that** I understand the organizational structure  

**Acceptance Criteria:**
- Visual organizational chart display
- Hierarchical list view of reporting relationships
- Search functionality within org structure
- Drill-down capability for detailed user information
- Export organizational chart to PDF
- Print-friendly organizational structure view

---

### **User Profile Management**

#### Story #14: Manage User Profiles
**As a** User  
**I want to** view and update my own profile information  
**So that** my details remain current in the system  

**Acceptance Criteria:**
- View personal profile information
- Edit contact details (phone, emergency contact)
- Change password functionality
- View assigned agency and supervisor
- Update notification preferences
- Cannot modify role or critical system fields

#### Story #15: Password Management
**As an** Admin Portal user  
**I want to** manage password requirements and resets  
**So that** account security is maintained  

**Acceptance Criteria:**
- Force password reset for users
- Set password expiration policies
- Password strength requirements
- Account lockout after failed attempts
- Password reset via email
- History to prevent password reuse

---

## 🗄️ Database Schema Requirements

### Current Database Structure (Implemented)

#### AdminUserModel (Current Implementation)
```php
<?php
// app/Models/AdminUserModel.php

namespace App\Models;
use CodeIgniter\Model;

class AdminUserModel extends Model
{
    protected $table = 'admin_users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password_hash', 'first_name', 'last_name',
        'phone_number', 'role', 'agency_id', 'is_active', 'created_by'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[admin_users.username,id,{id}]',
        'email' => 'required|valid_email|max_length[100]|is_unique[admin_users.email,id,{id}]',
        'password_hash' => 'required|min_length[4]',
        'first_name' => 'required|min_length[2]|max_length[50]',
        'last_name' => 'required|min_length[2]|max_length[50]',
        'phone_number' => 'permit_empty|max_length[20]',
        'role' => 'required|in_list[supervisor,user,admin]',
        'agency_id' => 'permit_empty|integer',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];
}
```

#### Current admin_users Table Structure
```php
<?php
// app/Database/Migrations/2024-01-01-000002_CreateAdminUsersTable.php

class CreateAdminUsersTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'auto_increment' => true],
            'username' => ['type' => 'VARCHAR', 'constraint' => 50],
            'email' => ['type' => 'VARCHAR', 'constraint' => 100],
            'password_hash' => ['type' => 'VARCHAR', 'constraint' => 255],
            'first_name' => ['type' => 'VARCHAR', 'constraint' => 50],
            'last_name' => ['type' => 'VARCHAR', 'constraint' => 50],
            'phone_number' => ['type' => 'VARCHAR', 'constraint' => 20, 'null' => true],
            'role' => ['type' => 'ENUM', 'constraint' => ['supervisor', 'user', 'admin'], 'default' => 'user'],
            'agency_id' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
            'is_active' => ['type' => 'TINYINT', 'constraint' => 1, 'default' => 1],
            'created_at' => ['type' => 'DATETIME', 'null' => true],
            'updated_at' => ['type' => 'DATETIME', 'null' => true],
            'created_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true]
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('username');
        $this->forge->addUniqueKey('email');
        $this->forge->addKey('role');
        $this->forge->addKey('agency_id');
        $this->forge->addKey('is_active');
        $this->forge->createTable('admin_users');
    }
}
```

#### Current agencies Table Structure
```php
<?php
// app/Database/Migrations/2024-01-01-000003_CreateAgenciesTable.php

class CreateAgenciesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'auto_increment' => true],
            'agency_code' => ['type' => 'VARCHAR', 'constraint' => 20],
            'name' => ['type' => 'VARCHAR', 'constraint' => 255],
            'address' => ['type' => 'TEXT', 'null' => true],
            'phone' => ['type' => 'VARCHAR', 'constraint' => 20, 'null' => true],
            'email' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true],
            'status' => ['type' => 'ENUM', 'constraint' => ['active', 'inactive', 'suspended'], 'default' => 'active'],
            'created_at' => ['type' => 'DATETIME', 'null' => true],
            'updated_at' => ['type' => 'DATETIME', 'null' => true],
            'created_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
            'updated_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true],
            'deleted_at' => ['type' => 'DATETIME', 'null' => true],
            'deleted_by' => ['type' => 'INT', 'constraint' => 11, 'unsigned' => true, 'null' => true]
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('agency_code');
        $this->forge->addKey('status');
        $this->forge->createTable('agencies');
    }
}
```

---

## 🎨 UI/UX Requirements

### Portal-Specific Theme Implementation

#### Admin Portal Theme (To Be Implemented)
- **Primary Colors**:
  - CHS Deep Blue (#1A4E8C) - Headers, primary buttons, navigation
  - CHS Red (#C8102E) - Accent elements, alerts, call-to-action
  - White (#FFFFFF) - Background, cards, content areas
  - Light Gray (#F8F9FA) - Secondary backgrounds, borders
  - Dark Gray (#343A40) - Text, icons

#### Dakoii Portal Theme (Current Implementation)
- **Dark Theme Colors**:
  - Dark backgrounds for extended admin use
  - Professional dark UI components
  - Bootstrap 5 with `data-bs-theme="dark"`
  - Implemented in `assets/css/dakoii-dark-theme.css`

### Bootstrap 5 Implementation
- **CSS Framework**: Bootstrap 5 (CDN: 5.3.2)
- **Layout**: Clean, professional card-based layout
- **Typography**: Professional fonts consistent with Bootstrap defaults
- **Components**: Theme-specific Bootstrap components
- **Icons**: Bootstrap Icons (CDN: 1.11.1)
- **Forms**: Professional form styling with validation states

### Current Navigation Structure (Dakoii Portal)
```html
<!-- Sidebar Navigation -->
<div class="sidebar d-flex flex-column p-3" id="sidebar">
    <!-- Logo and Brand -->
    <div class="text-center mb-4">
        <img src="assets/images/dakoii-logo.png" alt="Dakoii Logo" class="login-logo mb-2">
        <h5 class="text-light mb-0">Dakoii Portal</h5>
        <small class="text-muted">Super Administrator</small>
    </div>
    <!-- Navigation Menu -->
    <!-- User Profile Section -->
</div>

<!-- Main Content Area -->
<div class="main-content">
    <!-- Top Bar -->
    <!-- Page Content -->
</div>
```

### Planned Admin Portal Navigation Structure
```html
<!-- Top Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
    <!-- CHS Logo and Brand -->
    <!-- User Profile Dropdown -->
    <!-- Notifications -->
    <!-- Logout -->
</nav>

<!-- Sidebar Navigation (Collapsible) -->
<div class="sidebar bg-white border-end">
    <!-- Dashboard -->
    <!-- User Management -->
    <!-- Agency Users -->
    <!-- Reports -->
    <!-- Settings -->
</div>

<!-- Main Content Area -->
<main class="main-content">
    <!-- Breadcrumb Navigation -->
    <!-- Page Content -->
</main>
```

### CodeIgniter 4 View Structure
```php
// Current Implementation (Dakoii Portal)
// app/Views/templates/dakoii_portal_template.php - Dark theme template
// app/Views/dakoii/ - Dakoii portal views

// To Be Implemented (Admin Portal)
// app/Views/templates/admin_portal_template.php - Light theme template
// app/Views/admin/ - All Admin portal views
//   ├── auth/
//   │   └── login.php
//   ├── dashboard/
//   │   └── index.php
//   ├── users/
//   │   ├── index.php
//   │   ├── create.php
//   │   ├── edit.php
//   │   └── view.php
//   ├── agency_users/
//   │   ├── index.php
//   │   ├── assign.php
//   │   └── transfer.php
//   ├── reports/
//   │   ├── user_distribution.php
//   │   └── org_chart.php
//   └── partials/
//       ├── sidebar.php
//       ├── topbar.php
//       └── footer.php
```

### Responsive Design Requirements
- **Desktop**: Full sidebar navigation, expanded cards
- **Tablet**: Collapsible sidebar, medium-sized cards
- **Mobile**: Hidden sidebar (hamburger menu), stacked cards
- **Print**: Optimized layouts for reports and org charts

---

## 🔐 Security Requirements

### Current Authentication Architecture

#### Multi-Portal Authentication System
The system implements separate authentication for different portal types:

1. **Dakoii Portal** (Super Admin) - `/dakoii` routes
   - Uses `dakoii_users` table
   - DakoiiAuth library (implemented)
   - DakoiiAuthFilter (implemented)
   - Session keys: `dakoii_user_id`, `dakoii_logged_in`, etc.

2. **Admin Portal** (Organization Admin) - `/admin` routes (to be implemented)
   - Uses `admin_users` table
   - AdminAuth library (to be implemented)
   - AdminAuthFilter (to be implemented)
   - Session keys: `admin_user_id`, `admin_logged_in`, etc.

#### Authentication Libraries Pattern
```php
// app/Libraries/AdminAuth.php - Authentication library for Admin Portal (to be implemented)
class AdminAuth
{
    public function attempt($identifier, $password)
    {
        // Authenticate against admin_users table
        // Check user status and permissions
        // Set session data with admin_ prefix
        // Route based on agency_id (empty = admin portal, populated = agency portal)
    }

    public function hasPermission($action, $resource)
    {
        // Check role-based permissions
        // Supervisor: full CRUD for users and agency assignments
        // User: limited access to own profile and view permissions
    }
}
```

#### Role-Based Access Control Pattern
```php
// app/Filters/AdminAuthFilter.php - Authentication filter for Admin Portal (to be implemented)
class AdminAuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is logged in to admin portal
        // Verify role permissions for requested action
        // Redirect to admin login if not authenticated
        // Return 403 if not authorized
    }
}
```

#### Security Implementation
- **Password Requirements**: Minimum 4 characters (configurable)
- **Form Validation**: CI4 Validation rules for all user inputs
- **XSS Protection**: Built-in XSS filtering for all form data
- **CSRF Protection**: Token-based CSRF protection for all forms
- **SQL Injection Prevention**: Query Builder with parameter binding
- **Session Security**: Separate session namespaces for each portal
- **Password Hashing**: PHP password_hash() with PASSWORD_DEFAULT

---

## 📱 Technical Requirements

### Current Tech Stack (Implemented)
- **Backend Framework**: CodeIgniter 4 (PHP 8.1+)
- **Database**: MySQL with MySQLi driver
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5
- **Development Environment**: XAMPP (Apache, MySQL, PHP)
- **Base URL**: http://localhost/chealthwokman/
- **Session Management**: CodeIgniter 4 Session Library
- **File Handling**: CI4 File Upload to public/uploads directory

### Portal-Specific UI Themes
- **Dakoii Portal**: Dark theme (implemented)
  - Dark backgrounds for extended admin use
  - Professional dark UI components
  - Bootstrap 5 with dark theme customizations

- **Admin Portal**: Light theme with CHS branding (to be implemented)
  - CHS Deep Blue (#1A4E8C) - Headers, primary buttons, navigation
  - CHS Red (#C8102E) - Accent elements, alerts, call-to-action
  - White (#FFFFFF) - Background, cards, content areas
  - Light Gray (#F8F9FA) - Secondary backgrounds, borders
  - Bootstrap 5 with CHS brand customizations

### Performance Requirements
- **Page Load Time**: < 2 seconds for dashboard and user lists
- **Search Response**: < 1 second for user search functionality
- **Concurrent Users**: Support 50+ simultaneous users
- **Database Optimization**: Proper indexing on frequently queried fields
- **Caching**: CI4 caching for frequently accessed data

### Browser & Device Support
- **Desktop Browsers**: Chrome, Firefox, Safari, Edge (latest 2 versions)
- **Mobile Devices**: Responsive design for tablets and phones
- **Screen Resolutions**: 1920x1080 down to 320px mobile width
- **Accessibility**: WCAG 2.1 AA compliance for screen readers

### CodeIgniter 4 Architecture Features
- **MVC Pattern**: Strict separation of Models, Views, Controllers
- **RESTful Routing**: Standard CI4 routing conventions
- **Models**: Extend CodeIgniter\Model with built-in validation
- **Controllers**: Extend BaseController with shared functionality
- **Views**: Template-based rendering with CI4 View system
- **Migrations**: Database schema management
- **Validation**: Built-in form validation with custom rules
- **Pagination**: Built-in pagination for user lists
- **File Uploads**: CI4 upload handling for profile images
- **Email Integration**: CI4 Email library for notifications

---

## 🗃️ Implementation Status & Roadmap

### Current Implementation Status ✅

#### Foundation (Completed)
- **Database Structure**: All required tables created and migrated
  - `dakoii_users` table (super admin users)
  - `admin_users` table (organization admin users)
  - `agencies` table (agency management)
- **Dakoii Portal**: Fully implemented super admin portal
  - DakoiiAuth library and DakoiiAuthFilter
  - Dark theme UI with Bootstrap 5
  - Dashboard, user management, agency management
- **Models**: Core models implemented
  - DakoiiUserModel, AdminUserModel, AgencyModel
- **Base Architecture**: CodeIgniter 4 MVC structure established

### Admin Portal Implementation Roadmap 🚧

#### Phase 1: Authentication & Base Structure (Week 1-2)
1. **Admin Authentication System**
   - Create AdminAuth library (following DakoiiAuth pattern)
   - Implement AdminAuthFilter for role-based access
   - Set up admin portal routing with `/admin` prefix
   - Create shared login logic based on user type

2. **Base Structure & Templates**
   - Create AdminBaseController (following DakoiiBaseController pattern)
   - Create admin portal template with CHS light theme
   - Implement responsive navigation structure
   - Set up admin portal CSS and JavaScript assets

#### Phase 2: Core User Management (Week 3-4)
1. **User CRUD Operations**
   - Create user listing with pagination and search
   - Implement user creation form with validation
   - Build user editing functionality
   - Add user deactivation/deletion with confirmations

2. **Dashboard Development**
   - Create admin dashboard with key metrics
   - Add user statistics and distribution widgets
   - Implement recent activity feed
   - Add quick action buttons and navigation

#### Phase 3: Agency User Management (Week 5-6)
1. **Agency Assignment Features**
   - Build agency user assignment interface
   - Create agency user distribution reports
   - Implement bulk assignment tools
   - Add user transfer functionality between agencies

2. **Reporting & Analytics**
   - Create organizational chart visualization
   - Build user distribution reports
   - Add export functionality for reports
   - Implement search and filter capabilities

#### Phase 4: Advanced Features & Polish (Week 7-8)
1. **Reporting Hierarchy**
   - Implement supervisor assignment functionality
   - Create organizational structure management
   - Add reporting relationship validation
   - Build hierarchical user displays

2. **Security & Optimization**
   - Implement advanced security measures
   - Add audit logging for all actions
   - Optimize database queries and indexing
   - Conduct security testing and bug fixes

### Current File Structure
```
app/
├── Controllers/
│   ├── BaseController.php
│   ├── Home.php
│   └── Dakoii/                          # Super Admin Portal (Implemented)
│       ├── DakoiiBaseController.php
│       ├── AuthController.php
│       ├── DashboardController.php
│       ├── AdminUsersController.php
│       └── DakoiiAgenciesController.php
│   └── Admin/                           # Organization Admin Portal (To Be Implemented)
│       ├── AdminBaseController.php
│       ├── AuthController.php
│       ├── DashboardController.php
│       ├── UsersController.php
│       ├── AgencyUsersController.php
│       └── ReportsController.php
├── Models/
│   ├── DakoiiUserModel.php             # Super admin users (Implemented)
│   ├── AdminUserModel.php              # Organization admin users (Implemented)
│   └── AgencyModel.php                 # Agencies (Implemented)
├── Views/
│   ├── templates/
│   │   └── dakoii_portal_template.php  # Dark theme template (Implemented)
│   ├── dakoii/                         # Super Admin Portal views (Implemented)
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── admin_users/
│   │   ├── agencies/
│   │   └── partials/
│   └── admin/                          # Organization Admin Portal views (To Be Implemented)
│       ├── auth/
│       ├── dashboard/
│       ├── users/
│       ├── agency_users/
│       ├── reports/
│       └── partials/
├── Filters/
│   ├── DakoiiAuthFilter.php           # Super admin auth filter (Implemented)
│   └── AdminAuthFilter.php            # Organization admin auth filter (To Be Implemented)
├── Libraries/
│   ├── DakoiiAuth.php                 # Super admin auth library (Implemented)
│   └── AdminAuth.php                  # Organization admin auth library (To Be Implemented)
└── Database/
    ├── Migrations/
    │   ├── 2024-01-01-000001_CreateDakoiiUsersTable.php    # Implemented
    │   ├── 2024-01-01-000002_CreateAdminUsersTable.php     # Implemented
    │   └── 2024-01-01-000003_CreateAgenciesTable.php       # Implemented
    └── Seeds/
        └── (To be created as needed)

public/
├── assets/
│   ├── css/
│   │   ├── dakoii-dark-theme.css       # Dark theme for Dakoii Portal (Implemented)
│   │   └── admin-light-theme.css       # Light theme for Admin Portal (To Be Implemented)
│   ├── js/
│   │   ├── dakoii-app.js              # Dakoii Portal JS (Implemented)
│   │   └── admin-app.js               # Admin Portal JS (To Be Implemented)
│   └── images/
│       ├── dakoii-logo.png            # Dakoii Portal logo (Implemented)
│       ├── chs-logo.png               # CHS logo for Admin Portal (To Be Implemented)
│       └── user-avatars/
└── uploads/                           # File upload directory
```

---

## 🔄 Integration Points

### Current Multi-Portal Architecture

#### Portal Hierarchy
1. **Dakoii Portal** (Super Admin) - Highest level access
   - Manages all admin users and agencies
   - System-wide configuration and oversight
   - Dark theme interface for extended use

2. **Admin Portal** (Organization Admin) - Mid-level access
   - Manages users within assigned agencies
   - Agency-specific user management
   - Light theme with CHS branding

3. **Agency Portal** (Future) - User-level access
   - Individual user access to agency-specific functions
   - Limited permissions based on role

#### Shared Database Components
- **agencies Table**: Shared across all portals for agency information
- **admin_users Table**: Used by both Dakoii and Admin portals
  - `agency_id = NULL`: Admin Portal users (organization-wide access)
  - `agency_id = value`: Agency Portal users (agency-specific access)
- **dakoii_users Table**: Exclusive to Dakoii Portal (super admin access)

#### Authentication Integration
- **Separate Session Namespaces**: Each portal maintains independent sessions
- **Shared Models**: AgencyModel used across portals for consistency
- **Role-Based Routing**: Authentication determines portal access level
- **Session Security**: Portal-specific session keys prevent cross-contamination

### API Endpoints for Inter-Portal Communication
- **User Validation**: Verify user permissions across portals
- **Agency Information**: Fetch agency details for user assignments
- **Activity Logging**: Shared audit trail system (to be implemented)
- **Report Generation**: Cross-portal reporting capabilities

### Current Integration Status
- **Dakoii ↔ Admin Portal**: Shared agency and user management
- **Admin ↔ Agency Portal**: User assignment and role management (future)
- **File Uploads**: Centralized in public/uploads directory
- **Email System**: CI4 Email library for notifications across portals

### Future Integration Possibilities
- **Employee Portal**: User management for employee access
- **External Systems**: NASFUND, IRC integration for user data
- **Mobile App**: API endpoints for mobile application access
- **Single Sign-On**: Unified authentication across all portals