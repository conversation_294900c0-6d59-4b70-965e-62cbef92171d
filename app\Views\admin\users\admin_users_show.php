<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">User Details</h1>
        <p class="text-muted mb-0">View user information and account details</p>
    </div>
    <div>
        <?php if ($adminUser['role'] !== 'admin'): ?>
        <a href="<?= base_url('/admin/users/' . $adminUser['id'] . '/edit') ?>" class="btn btn-primary me-2">
            <i class="material-icons me-2">edit</i>
            Edit User
        </a>
        <?php else: ?>
        <button type="button" class="btn btn-secondary me-2" disabled title="Admin users cannot be edited">
            <i class="material-icons me-2">edit</i>
            Edit User
        </button>
        <?php endif; ?>
        <a href="<?= base_url('/admin/users') ?>" class="btn btn-outline-secondary">
            <i class="material-icons me-2">arrow_back</i>
            Back to List
        </a>
    </div>
</div>

<div class="row">
    <!-- User Profile Card -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="user-avatar mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                    <?= strtoupper(substr($adminUser['first_name'], 0, 1) . substr($adminUser['last_name'], 0, 1)) ?>
                </div>
                <h4><?= esc($adminUser['first_name'] . ' ' . $adminUser['last_name']) ?></h4>
                <p class="text-muted mb-2">@<?= esc($adminUser['username']) ?></p>
                
                <div class="d-flex justify-content-center gap-2 mb-3">
                    <span class="badge bg-<?= $adminUser['role'] === 'admin' ? 'danger' : ($adminUser['role'] === 'supervisor' ? 'warning' : 'info') ?>">
                        <?= ucfirst(esc($adminUser['role'])) ?>
                    </span>
                    <span class="badge bg-<?= $adminUser['is_active'] ? 'success' : 'secondary' ?>">
                        <?= $adminUser['is_active'] ? 'Active' : 'Inactive' ?>
                    </span>
                </div>

                <?php if ($agency): ?>
                <div class="alert alert-info">
                    <i class="material-icons me-2">business</i>
                    <strong>Agency User</strong><br>
                    <small><?= esc($agency['agency_code']) ?> - <?= esc($agency['name']) ?></small>
                </div>
                <?php else: ?>
                <div class="alert alert-primary">
                    <i class="material-icons me-2">admin_panel_settings</i>
                    <strong>Admin Portal User</strong><br>
                    <small>System-wide access</small>
                </div>
                <?php endif; ?>

                <div class="d-grid gap-2">
                    <?php if ($adminUser['role'] !== 'admin'): ?>
                    <a href="<?= base_url('/admin/users/' . $adminUser['id'] . '/edit') ?>" class="btn btn-outline-primary">
                        <i class="material-icons me-2">edit</i>
                        Edit Profile
                    </a>
                    <?php else: ?>
                    <button type="button" class="btn btn-outline-secondary" disabled title="Admin users cannot be edited">
                        <i class="material-icons me-2">edit</i>
                        Edit Profile
                    </button>
                    <?php endif; ?>
                    <?php if ($adminUser['id'] != $currentUser['id'] && $adminUser['role'] !== 'admin'): ?>
                    <button type="button"
                            class="btn btn-outline-danger"
                            onclick="confirmDelete(<?= $adminUser['id'] ?>, '<?= esc($adminUser['first_name'] . ' ' . $adminUser['last_name']) ?>')">
                        <i class="material-icons me-2">delete</i>
                        Delete User
                    </button>
                    <?php elseif ($adminUser['role'] === 'admin'): ?>
                    <button type="button" class="btn btn-outline-secondary" disabled title="Admin users cannot be deleted">
                        <i class="material-icons me-2">delete</i>
                        Delete User
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons me-2">person</i>
                    Personal Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">First Name</label>
                        <p class="fw-bold"><?= esc($adminUser['first_name']) ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Last Name</label>
                        <p class="fw-bold"><?= esc($adminUser['last_name']) ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Username</label>
                        <p class="fw-bold">@<?= esc($adminUser['username']) ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Email Address</label>
                        <p class="fw-bold">
                            <a href="mailto:<?= esc($adminUser['email']) ?>" class="text-decoration-none">
                                <?= esc($adminUser['email']) ?>
                            </a>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Phone Number</label>
                        <p class="fw-bold">
                            <?php if (!empty($adminUser['phone_number'])): ?>
                                <a href="tel:<?= esc($adminUser['phone_number']) ?>" class="text-decoration-none">
                                    <?= esc($adminUser['phone_number']) ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">Not provided</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons me-2">settings</i>
                    Account Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">User ID</label>
                        <p class="fw-bold">#<?= $adminUser['id'] ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Role</label>
                        <p>
                            <span class="badge bg-<?= $adminUser['role'] === 'admin' ? 'danger' : ($adminUser['role'] === 'supervisor' ? 'warning' : 'info') ?> fs-6">
                                <?= ucfirst(esc($adminUser['role'])) ?>
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Account Status</label>
                        <p>
                            <span class="badge bg-<?= $adminUser['is_active'] ? 'success' : 'secondary' ?> fs-6">
                                <?= $adminUser['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Portal Access</label>
                        <p>
                            <?php if ($agency): ?>
                                <span class="badge bg-secondary fs-6">Agency Portal</span>
                            <?php else: ?>
                                <span class="badge bg-primary fs-6">Admin Portal</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Created Date</label>
                        <p class="fw-bold"><?= date('F j, Y \a\t g:i A', strtotime($adminUser['created_at'])) ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <p class="fw-bold"><?= date('F j, Y \a\t g:i A', strtotime($adminUser['updated_at'])) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agency Information (if applicable) -->
        <?php if ($agency): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons me-2">business</i>
                    Agency Assignment
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Agency Code</label>
                        <p class="fw-bold"><?= esc($agency['agency_code']) ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Agency Name</label>
                        <p class="fw-bold"><?= esc($agency['name']) ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Agency Status</label>
                        <p>
                            <span class="badge bg-<?= $agency['status'] === 'active' ? 'success' : ($agency['status'] === 'inactive' ? 'secondary' : 'warning') ?> fs-6">
                                <?= ucfirst(esc($agency['status'])) ?>
                            </span>
                        </p>
                    </div>
                    <?php if (!empty($agency['email'])): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Agency Email</label>
                        <p class="fw-bold">
                            <a href="mailto:<?= esc($agency['email']) ?>" class="text-decoration-none">
                                <?= esc($agency['email']) ?>
                            </a>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="material-icons me-2">warning</i>
                    Confirm Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons me-1">delete</i>
                        Delete User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    background: linear-gradient(135deg, #1A4E8C 0%, #2563EB 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.fs-6 {
    font-size: 0.875rem !important;
}
</style>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = '<?= base_url('/admin/users/') ?>' + userId + '/delete';
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
