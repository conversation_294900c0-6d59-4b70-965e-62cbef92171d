<?php

namespace App\Controllers\Dakoii;

use App\Controllers\Dakoii\DakoiiBaseController;
use App\Models\AgencyModel;

class DakoiiAgenciesController extends DakoiiBaseController
{
    protected $agencyModel;

    public function __construct()
    {
        $this->agencyModel = new AgencyModel();
    }

    /**
     * Display list of agencies (GET)
     */
    public function index()
    {
        $this->setPageTitle('Agencies');
        $this->setSidebarActive('agencies');
        $this->setBreadcrumbs([
            ['title' => 'Agencies', 'url' => '/dakoii/agencies', 'active' => true]
        ]);

        $search = $this->request->getGet('search') ?? '';
        $perPage = 10;

        $data = [
            'agencies' => $this->agencyModel->getAgencies($perPage, $search),
            'pager' => $this->agencyModel->pager,
            'search' => $search,
            'stats' => $this->agencyModel->getAgencyStats(),
        ];

        return $this->render('dakoii/agencies/agencies_index', $data);
    }

    /**
     * Display create form (GET)
     */
    public function create()
    {
        $this->setPageTitle('Create Agency');
        $this->setSidebarActive('agencies_create');
        $this->setBreadcrumbs([
            ['title' => 'Agencies', 'url' => '/dakoii/agencies', 'active' => false],
            ['title' => 'Create New', 'url' => '/dakoii/agencies/create', 'active' => true]
        ]);

        $data = [
            'statusOptions' => $this->agencyModel->getStatusOptions(),
            'validation' => \Config\Services::validation(),
        ];

        return $this->render('dakoii/agencies/agencies_create', $data);
    }

    /**
     * Process create form (POST)
     */
    public function store()
    {
        // Basic validation rules (without unique checks)
        $rules = [
            'agency_code' => 'required|min_length[2]|max_length[20]',
            'name' => 'required|min_length[3]|max_length[255]',
            'address' => 'permit_empty|max_length[1000]',
            'phone' => 'permit_empty|max_length[20]',
            'email' => 'permit_empty|valid_email|max_length[100]',
            'status' => 'required|in_list[active,inactive,suspended]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // Check agency code uniqueness
        $agencyCode = $this->request->getPost('agency_code');
        if (!$this->agencyModel->isAgencyCodeUnique($agencyCode)) {
            $this->setFlashMessage('error', 'Agency code already exists.');
            return redirect()->back()->withInput();
        }

        // Prepare data for insertion
        $data = [
            'agency_code' => strtoupper($agencyCode),
            'name' => $this->request->getPost('name'),
            'address' => $this->request->getPost('address'),
            'phone' => $this->request->getPost('phone'),
            'email' => $this->request->getPost('email'),
            'status' => $this->request->getPost('status'),
            'created_by' => $this->getCurrentUserId()
        ];

        // Skip model validation since we did our own
        $this->agencyModel->skipValidation(true);
        if ($this->agencyModel->insert($data)) {
            $this->setFlashMessage('success', 'Agency created successfully.');
            return redirect()->to('/dakoii/agencies');
        }

        $this->setFlashMessage('error', 'Failed to create agency. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Display single agency (GET)
     */
    public function show($id)
    {
        $agency = $this->agencyModel->getAgency($id);
        
        if (!$agency) {
            $this->setFlashMessage('error', 'Agency not found.');
            return redirect()->to('/dakoii/agencies');
        }

        $this->setPageTitle('View Agency');
        $this->setSidebarActive('agencies');
        $this->setBreadcrumbs([
            ['title' => 'Agencies', 'url' => '/dakoii/agencies', 'active' => false],
            ['title' => $agency['name'], 'url' => '/dakoii/agencies/' . $id, 'active' => true]
        ]);

        $data = [
            'agency' => $agency,
            'statusOptions' => $this->agencyModel->getStatusOptions(),
        ];

        return $this->render('dakoii/agencies/agencies_show', $data);
    }

    /**
     * Display edit form (GET)
     */
    public function edit($id)
    {
        $agency = $this->agencyModel->getAgency($id);
        
        if (!$agency) {
            $this->setFlashMessage('error', 'Agency not found.');
            return redirect()->to('/dakoii/agencies');
        }

        $this->setPageTitle('Edit Agency');
        $this->setSidebarActive('agencies_edit');
        $this->setBreadcrumbs([
            ['title' => 'Agencies', 'url' => '/dakoii/agencies', 'active' => false],
            ['title' => $agency['name'], 'url' => '/dakoii/agencies/' . $id, 'active' => false],
            ['title' => 'Edit', 'url' => '/dakoii/agencies/' . $id . '/edit', 'active' => true]
        ]);

        $data = [
            'agency' => $agency,
            'statusOptions' => $this->agencyModel->getStatusOptions(),
            'validation' => \Config\Services::validation(),
        ];

        return $this->render('dakoii/agencies/agencies_edit', $data);
    }

    /**
     * Process edit form (POST)
     */
    public function update($id)
    {
        $agency = $this->agencyModel->getAgency($id);
        
        if (!$agency) {
            $this->setFlashMessage('error', 'Agency not found.');
            return redirect()->to('/dakoii/agencies');
        }

        // Basic validation rules (without unique checks)
        $rules = [
            'agency_code' => 'required|min_length[2]|max_length[20]',
            'name' => 'required|min_length[3]|max_length[255]',
            'address' => 'permit_empty|max_length[1000]',
            'phone' => 'permit_empty|max_length[20]',
            'email' => 'permit_empty|valid_email|max_length[100]',
            'status' => 'required|in_list[active,inactive,suspended]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // Check agency code uniqueness (exclude current agency)
        $agencyCode = $this->request->getPost('agency_code');
        if (!$this->agencyModel->isAgencyCodeUnique($agencyCode, $id)) {
            $this->setFlashMessage('error', 'Agency code already exists.');
            return redirect()->back()->withInput();
        }

        // Prepare data for update
        $data = [
            'agency_code' => strtoupper($agencyCode),
            'name' => $this->request->getPost('name'),
            'address' => $this->request->getPost('address'),
            'phone' => $this->request->getPost('phone'),
            'email' => $this->request->getPost('email'),
            'status' => $this->request->getPost('status'),
            'updated_by' => $this->getCurrentUserId()
        ];

        // Skip model validation since we did our own
        $this->agencyModel->skipValidation(true);
        if ($this->agencyModel->update($id, $data)) {
            $this->setFlashMessage('success', 'Agency updated successfully.');
            return redirect()->to('/dakoii/agencies/' . $id);
        }

        $this->setFlashMessage('error', 'Failed to update agency. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Delete agency (POST)
     */
    public function delete($id)
    {
        $agency = $this->agencyModel->getAgency($id);
        
        if (!$agency) {
            $this->setFlashMessage('error', 'Agency not found.');
            return redirect()->to('/dakoii/agencies');
        }

        if ($this->agencyModel->delete($id)) {
            $this->setFlashMessage('success', 'Agency deleted successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to delete agency. Please try again.');
        }

        return redirect()->to('/dakoii/agencies');
    }
}
