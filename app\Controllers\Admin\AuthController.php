<?php

namespace App\Controllers\Admin;

use App\Controllers\Admin\AdminBaseController;

class AuthController extends AdminBaseController
{
    /**
     * Display login form (GET)
     */
    public function login()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/admin/dashboard');
        }

        $this->setPageTitle('Login');
        
        $data = [
            'validation' => \Config\Services::validation(),
        ];

        return view('admin/auth/login', $data);
    }

    /**
     * Process login form (POST)
     */
    public function authenticate()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/admin/dashboard');
        }

        // Validation rules
        $rules = [
            'identifier' => [
                'label' => 'Username/Email',
                'rules' => 'required|min_length[3]|max_length[100]'
            ],
            'password' => [
                'label' => 'Password',
                'rules' => 'required|min_length[4]'
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        $identifier = $this->request->getPost('identifier');
        $password = $this->request->getPost('password');

        // Attempt login
        if ($this->auth->attempt($identifier, $password)) {
            // Check for intended URL
            $intendedUrl = $this->session->get('admin_intended_url');
            $this->session->remove('admin_intended_url');

            // Determine redirect URL based on user's agency assignment
            $redirectUrl = $intendedUrl ?: $this->auth->getRedirectUrl();
            
            $this->setFlashMessage('success', 'Welcome back, ' . $this->auth->fullName() . '!');
            return redirect()->to($redirectUrl);
        }

        // Login failed
        $this->setFlashMessage('error', 'Invalid username/email or password. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Log out user (POST)
     */
    public function logout()
    {
        if ($this->isAuthenticated()) {
            $this->auth->logout();
            $this->setFlashMessage('success', 'You have been logged out successfully.');
        }

        return redirect()->to('/admin/login');
    }

    /**
     * Display forgot password form (GET)
     */
    public function forgotPassword()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/admin/dashboard');
        }

        $this->setPageTitle('Forgot Password');
        
        $data = [
            'validation' => \Config\Services::validation(),
        ];

        return view('admin/auth/forgot_password', $data);
    }

    /**
     * Process forgot password form (POST)
     */
    public function sendResetLink()
    {
        // Redirect to dashboard if already logged in
        if ($this->isAuthenticated()) {
            return redirect()->to('/admin/dashboard');
        }

        // Validation rules
        $rules = [
            'email' => [
                'label' => 'Email',
                'rules' => 'required|valid_email'
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        $email = $this->request->getPost('email');

        // TODO: Implement password reset functionality
        // For now, just show a success message
        $this->setFlashMessage('success', 'If an account with that email exists, a password reset link has been sent.');
        return redirect()->to('/admin/login');
    }
}
