<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-light">Edit Agency</h1>
        <p class="text-muted mb-0">Update agency information and settings</p>
    </div>
    <div>
        <a href="<?= base_url('dakoii/agencies/' . $agency['id']) ?>" class="btn btn-outline-info me-2">
            <i class="bi bi-eye me-2"></i>
            View Agency
        </a>
        <a href="<?= base_url('dakoii/agencies') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to List
        </a>
    </div>
</div>

<!-- Edit Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pencil me-2"></i>
                    Edit: <?= esc($agency['name']) ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/update') ?>">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <!-- Agency Code -->
                        <div class="col-md-6 mb-3">
                            <label for="agency_code" class="form-label">
                                Agency Code <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('agency_code')) ? 'is-invalid' : '' ?>" 
                                   id="agency_code" 
                                   name="agency_code" 
                                   value="<?= old('agency_code', $agency['agency_code']) ?>" 
                                   style="text-transform: uppercase;"
                                   required>
                            <div class="form-text">Unique code for the agency</div>
                            <?php if (isset($validation) && $validation->hasError('agency_code')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('agency_code') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">
                                Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= (isset($validation) && $validation->hasError('status')) ? 'is-invalid' : '' ?>" 
                                    id="status" 
                                    name="status" 
                                    required>
                                <option value="">Select Status</option>
                                <?php foreach ($statusOptions as $value => $label): ?>
                                    <option value="<?= esc($value) ?>" <?= (old('status', $agency['status']) === $value) ? 'selected' : '' ?>>
                                        <?= esc($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (isset($validation) && $validation->hasError('status')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('status') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Agency Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            Agency Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control <?= (isset($validation) && $validation->hasError('name')) ? 'is-invalid' : '' ?>" 
                               id="name" 
                               name="name" 
                               value="<?= old('name', $agency['name']) ?>" 
                               required>
                        <?php if (isset($validation) && $validation->hasError('name')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('name') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Address -->
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control <?= (isset($validation) && $validation->hasError('address')) ? 'is-invalid' : '' ?>" 
                                  id="address" 
                                  name="address" 
                                  rows="3"
                                  placeholder="Enter agency address..."><?= old('address', $agency['address']) ?></textarea>
                        <?php if (isset($validation) && $validation->hasError('address')): ?>
                            <div class="invalid-feedback">
                                <?= $validation->getError('address') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="row">
                        <!-- Phone -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('phone')) ? 'is-invalid' : '' ?>" 
                                   id="phone" 
                                   name="phone" 
                                   value="<?= old('phone', $agency['phone']) ?>" 
                                   placeholder="+************">
                            <?php if (isset($validation) && $validation->hasError('phone')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('phone') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" 
                                   class="form-control <?= (isset($validation) && $validation->hasError('email')) ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= old('email', $agency['email']) ?>" 
                                   placeholder="<EMAIL>">
                            <?php if (isset($validation) && $validation->hasError('email')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('email') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <a href="<?= base_url('dakoii/agencies/' . $agency['id']) ?>" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            Update Agency
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Info Sidebar -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Agency Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Created:</small>
                    <div><?= date('M j, Y g:i A', strtotime($agency['created_at'])) ?></div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Last Updated:</small>
                    <div><?= date('M j, Y g:i A', strtotime($agency['updated_at'])) ?></div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Current Status:</small>
                    <div>
                        <?php
                        $statusClass = 'bg-secondary';
                        if ($agency['status'] === 'active') $statusClass = 'bg-success';
                        elseif ($agency['status'] === 'inactive') $statusClass = 'bg-warning';
                        elseif ($agency['status'] === 'suspended') $statusClass = 'bg-danger';
                        ?>
                        <span class="badge <?= $statusClass ?>">
                            <?= ucfirst(esc($agency['status'])) ?>
                        </span>
                    </div>
                </div>
                
                <hr>
                
                <h6 class="text-primary">Status Descriptions:</h6>
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <strong class="text-success">Active:</strong> Fully operational
                    </li>
                    <li class="mb-2">
                        <strong class="text-warning">Inactive:</strong> Temporarily not operational
                    </li>
                    <li class="mb-2">
                        <strong class="text-danger">Suspended:</strong> Operations suspended
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Danger Zone -->
        <div class="card border-danger mt-3">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Danger Zone
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    Permanently delete this agency. This action cannot be undone.
                </p>
                <form method="POST" 
                      action="<?= base_url('dakoii/agencies/' . $agency['id'] . '/delete') ?>" 
                      onsubmit="return confirm('Are you sure you want to delete this agency? This action cannot be undone.')">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger btn-sm">
                        <i class="bi bi-trash me-2"></i>
                        Delete Agency
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-uppercase agency code
document.getElementById('agency_code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});
</script>
