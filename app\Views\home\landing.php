<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHealth Wokman - Workforce Management System | Christian Health Services Papua New Guinea</title>
    <meta name="description" content="CHealth Wokman - Workforce Management Information System for Christian Health Services PNG. Managing staff, schedules, and human resources across CHS facilities.">
    <link rel="shortcut icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Bootstrap -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #1A4E8C;
            --engine-red: #C8102E;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --gradient-primary: linear-gradient(135deg, #1A4E8C 0%, #2563EB 100%);
            --gradient-secondary: linear-gradient(135deg, #C8102E 0%, #EF4444 100%);
            --gradient-hero: linear-gradient(135deg, #1A4E8C 0%, #2563EB 50%, #C8102E 100%);
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
        }

        .hero-section {
            background: var(--gradient-hero);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 80px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-family: 'Roboto Condensed', sans-serif;
            font-weight: 700;
            font-size: 3.5rem;
            color: var(--text-light);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2rem;
        }

        .chs-logo {
            width: 180px;
            height: 180px;
            background: var(--text-light);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
            padding: 20px;
        }

        .chs-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .system-logo {
            width: 120px;
            height: auto;
            margin-bottom: 1rem;
        }

        .btn-custom {
            background: var(--gradient-secondary);
            border: none;
            padding: 12px 30px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(200, 16, 46, 0.3);
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(200, 16, 46, 0.4);
        }

        .features-section {
            padding: 80px 0;
            background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
        }

        .feature-card {
            background: var(--text-light);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 35px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: var(--text-light);
            font-size: 2rem;
        }

        .section-title {
            font-family: 'Roboto Condensed', sans-serif;
            font-weight: 700;
            font-size: 2.5rem;
            color: var(--primary-blue);
            text-align: center;
            margin-bottom: 3rem;
        }

        .stats-section {
            background: var(--gradient-primary);
            padding: 60px 0;
            color: var(--text-light);
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .footer {
            background: var(--text-dark);
            color: var(--text-light);
            padding: 40px 0 20px;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .navbar-brand img {
            height: 40px;
        }

        .navbar {
            background: rgba(26, 78, 140, 0.95) !important;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: var(--primary-blue) !important;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .chs-logo {
                width: 120px;
                height: 120px;
            }

            .system-logo {
                width: 100px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url('assets/images/system-logo.png') ?>" alt="CHealth Wokman" class="me-2">
                <span class="fw-bold">CHealth Wokman</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Workforce Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About CHS</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light ms-2 px-3" href="<?= base_url('admin/login') ?>">Admin Login</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <div class="chs-logo">
                            <img src="<?= base_url('assets/images/chs-logo.png') ?>" alt="Christian Health Services PNG Logo">
                        </div>
                        <h1 class="hero-title">Christian Health Services</h1>
                        <p class="hero-subtitle">Papua New Guinea</p>
                        <p class="lead text-white mb-4">"To serve with love, dedication, compassion and commitment in humility."</p>
                        <div class="d-flex gap-3 flex-wrap">
                            <a href="#services" class="btn btn-custom text-white">Our Services</a>
                            <a href="#about" class="btn btn-outline-light">Learn More</a>
                            <a href="<?= base_url('admin/login') ?>" class="btn btn-danger">
                                <i class="material-icons me-2">login</i>
                                Admin Portal Login
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <img src="<?= base_url('assets/images/system-logo.png') ?>" alt="CHealth Wokman System" class="system-logo">
                        <h3 class="text-white mt-3">CHealth Wokman System</h3>
                        <p class="text-white-50">Workforce Management Information System</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="services">
        <div class="container">
            <h2 class="section-title">Workforce Management Features</h2>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">person</i>
                        </div>
                        <h4>Staff Management</h4>
                        <p>Comprehensive employee records, profiles, and personal information management for all CHS staff.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">schedule</i>
                        </div>
                        <h4>Scheduling & Roster</h4>
                        <p>Work schedule management, shift planning, and duty roster creation for healthcare workers.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">access_time</i>
                        </div>
                        <h4>Time & Attendance</h4>
                        <p>Track working hours, attendance records, and manage leave applications across CHS facilities.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">school</i>
                        </div>
                        <h4>Training Records</h4>
                        <p>Manage employee training history, certifications, and professional development tracking.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">assessment</i>
                        </div>
                        <h4>Performance Management</h4>
                        <p>Employee performance tracking, evaluations, and workforce analytics and reporting.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">place</i>
                        </div>
                        <h4>Multi-Facility Support</h4>
                        <p>Manage workforce across multiple CHS facilities and agencies throughout Papua New Guinea.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- System Benefits Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-12 mb-4">
                    <h2 class="text-white mb-4">Workforce Management Benefits</h2>
                    <p class="text-white-50">Supporting CHS mission through efficient human resource management</p>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <i class="material-icons" style="font-size: 3rem; margin-bottom: 1rem;">speed</i>
                        <span class="stat-label">Operational Efficiency</span>
                        <p class="mt-2 small">Streamlined workforce processes across all facilities</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <i class="material-icons" style="font-size: 3rem; margin-bottom: 1rem;">visibility</i>
                        <span class="stat-label">Real-time Visibility</span>
                        <p class="mt-2 small">Complete oversight of staff allocation and availability</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <i class="material-icons" style="font-size: 3rem; margin-bottom: 1rem;">compliance</i>
                        <span class="stat-label">Compliance Management</span>
                        <p class="mt-2 small">Ensure regulatory compliance and certification tracking</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <i class="material-icons" style="font-size: 3rem; margin-bottom: 1rem;">analytics</i>
                        <span class="stat-label">Data-Driven Decisions</span>
                        <p class="mt-2 small">Workforce analytics and reporting for better planning</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="py-5" id="about">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="section-title text-start">About Christian Health Services PNG</h2>
                    <div class="mb-4">
                        <h5 class="text-primary">Our Vision</h5>
                        <p class="mb-3">"Church Health Services as a constitutional organization will strive to carry out the healing ministry of Jesus Christ by providing quality and affordable health care for all people."</p>
                    </div>
                    <div class="mb-4">
                        <h5 class="text-primary">Our Mission</h5>
                        <p class="mb-3">"Inspired by the Gospel of Jesus Christ, we strive to provide the best health care with dignity, respect, compassion and dedication in partnership with government and non-government health care providers through health promotion, training, clinical care and evidence based research work."</p>
                    </div>
                    <div>
                        <h5 class="text-primary">Our Core Values</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="material-icons text-primary me-2">favorite</i><strong>Prayer:</strong> Strategic prayer as a prerequisite for success</li>
                            <li class="mb-2"><i class="material-icons text-primary me-2">groups</i><strong>Christian Unity:</strong> Promote unity and cooperation across denominations</li>
                            <li class="mb-2"><i class="material-icons text-primary me-2">person</i><strong>Dignity:</strong> All people have intrinsic value and deserve opportunity</li>
                            <li class="mb-2"><i class="material-icons text-primary me-2">healing</i><strong>Integration:</strong> Integrate physical, social, spiritual and mental care</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <div class="bg-light rounded p-5" style="min-height: 300px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                            <img src="<?= base_url('assets/images/system-logo.png') ?>" alt="CHealth Wokman System" style="max-width: 200px; margin-bottom: 20px;">
                            <h4 style="color: var(--primary-blue);">CHealth Wokman System</h4>
                            <p class="text-muted">Workforce Management Information System</p>
                            <p class="text-center small">Streamlining human resource management across all Christian Health Services facilities in Papua New Guinea.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Christian Health Services PNG</h5>
                    <p class="mb-3">"To serve with love, dedication, compassion and commitment in humility."</p>
                    <p>Inspired by the Gospel of Jesus Christ, we provide quality healthcare with dignity, respect, and compassion across Papua New Guinea.</p>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Our Priorities</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">Health Services Standards</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Health Infrastructure</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Financial Management</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Human Resource</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Governance</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6>System Features</h6>
                    <ul class="list-unstyled">
                        <li><a href="#services" class="text-light text-decoration-none">Staff Management</a></li>
                        <li><a href="#services" class="text-light text-decoration-none">Scheduling & Roster</a></li>
                        <li><a href="#services" class="text-light text-decoration-none">Time & Attendance</a></li>
                        <li><a href="#services" class="text-light text-decoration-none">Training Records</a></li>
                        <li><a href="#services" class="text-light text-decoration-none">Performance Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6>Contact Information</h6>
                    <p class="mb-1"><i class="material-icons me-2" style="font-size: 1rem;">location_on</i>Lot 2, Section 22, Geregere Avenue, East Boroko</p>
                    <p class="mb-1"><i class="material-icons me-2" style="font-size: 1rem;">mail</i>P O Box 3269, BOROKO, NCD, PNG</p>
                    <p class="mb-1"><i class="material-icons me-2" style="font-size: 1rem;">phone</i>(+************* or 325 3368</p>
                    <p class="mb-1"><i class="material-icons me-2" style="font-size: 1rem;">email</i><a href="mailto:<EMAIL>" class="text-light text-decoration-none"><EMAIL></a></p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <p class="mb-0">&copy; 2025 Christian Health Services PNG. All rights reserved.</p>
                </div>
                <div class="col-md-4 text-center">
                    <p class="mb-0">"To serve with love, dedication, compassion and commitment in humility."</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end justify-content-center">
                        <img src="<?= base_url('assets/images/dakoii-logo.png') ?>" alt="Dakoii Systems" style="height: 30px; margin-right: 10px;">
                        <div class="text-start">
                            <small class="d-block">Developed by <a href="https://www.dakoiims.com" target="_blank" class="text-light text-decoration-none">Dakoii Systems</a></small>
                            <small class="d-block"><a href="mailto:<EMAIL>" class="text-light text-decoration-none"><EMAIL></a></small>
                            <small><a href="https://www.dakoiims.com" target="_blank" class="text-light text-decoration-none">www.dakoiims.com</a></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Material Design Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>

    <script>
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>
