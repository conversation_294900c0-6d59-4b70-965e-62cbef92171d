/* Admin Portal Light Theme */
/* CHS Brand Colors:
   - Deep Cerulean Blue: #1A4E8C
   - Engine Red: #C8102E
   - White: #FFFFFF
   - Light Gray: #F8F9FA
   - Border Gray: #DEE2E6
*/

:root {
    --admin-primary: #1A4E8C;
    --admin-danger: #C8102E;
    --admin-success: #28a745;
    --admin-warning: #ffc107;
    --admin-info: #17a2b8;
    --admin-light: #F8F9FA;
    --admin-white: #FFFFFF;
    --admin-dark: #343A40;
    --admin-border: #DEE2E6;
    --admin-muted: #6c757d;
}

/* Global Light Theme */
body {
    background-color: var(--admin-light);
    color: var(--admin-dark);
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
}

/* Bootstrap Overrides */
.bg-primary {
    background-color: var(--admin-primary) !important;
}

.bg-danger {
    background-color: var(--admin-danger) !important;
}

.text-primary {
    color: var(--admin-primary) !important;
}

.text-danger {
    color: var(--admin-danger) !important;
}

.border-primary {
    border-color: var(--admin-primary) !important;
}

/* Cards */
.card {
    background-color: var(--admin-white);
    border: 1px solid var(--admin-border);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #2563EB 100%);
    color: var(--admin-white);
    border-bottom: none;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Forms */
.form-control {
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 0.75rem;
    background-color: var(--admin-white);
    color: var(--admin-dark);
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
    background-color: var(--admin-white);
    color: var(--admin-dark);
}

.form-select {
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    background-color: var(--admin-white);
    color: var(--admin-dark);
}

.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(26, 78, 140, 0.25);
}

.form-label {
    color: var(--admin-dark);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #2563EB 100%);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #164a87 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(26, 78, 140, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--admin-danger) 0%, #EF4444 100%);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #b30e29 0%, #dc2626 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(200, 16, 46, 0.3);
}

.btn-outline-primary {
    color: var(--admin-primary);
    border-color: var(--admin-primary);
    border-radius: 8px;
}

.btn-outline-primary:hover {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.btn-outline-secondary {
    border-radius: 8px;
}

/* Tables */
.table {
    background-color: var(--admin-white);
    color: var(--admin-dark);
}

.table th {
    background-color: var(--admin-light);
    color: var(--admin-dark);
    font-weight: 600;
    border-bottom: 2px solid var(--admin-border);
}

.table td {
    border-bottom: 1px solid var(--admin-border);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(248, 249, 250, 0.5);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(26, 78, 140, 0.1);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    padding: 1rem 1.5rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left: 4px solid var(--admin-success);
}

.alert-danger {
    background-color: rgba(200, 16, 46, 0.1);
    color: #721c24;
    border-left: 4px solid var(--admin-danger);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--admin-warning);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-left: 4px solid var(--admin-info);
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

.badge.bg-primary {
    background-color: var(--admin-primary) !important;
}

.badge.bg-danger {
    background-color: var(--admin-danger) !important;
}

.badge.bg-success {
    background-color: var(--admin-success) !important;
}

/* Pagination */
.pagination .page-link {
    color: var(--admin-primary);
    border-color: var(--admin-border);
    background-color: var(--admin-white);
}

.pagination .page-link:hover {
    color: var(--admin-white);
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.pagination .page-item.active .page-link {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* Dropdowns */
.dropdown-menu {
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    background-color: var(--admin-white);
}

.dropdown-item {
    color: var(--admin-dark);
}

.dropdown-item:hover {
    background-color: var(--admin-light);
    color: var(--admin-primary);
}

/* Modal */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #2563EB 100%);
    color: var(--admin-white);
    border-bottom: none;
    border-radius: 10px 10px 0 0;
}

.modal-body {
    background-color: var(--admin-white);
    color: var(--admin-dark);
}

.modal-footer {
    background-color: var(--admin-light);
    border-top: 1px solid var(--admin-border);
    border-radius: 0 0 10px 10px;
}

/* Progress bars */
.progress {
    background-color: var(--admin-light);
    border-radius: 8px;
}

.progress-bar {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #2563EB 100%);
}

/* Custom utilities */
.text-muted {
    color: var(--admin-muted) !important;
}

.bg-light {
    background-color: var(--admin-light) !important;
}

.border {
    border-color: var(--admin-border) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
}
